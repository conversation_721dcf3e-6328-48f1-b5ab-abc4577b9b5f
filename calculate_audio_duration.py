import os
import argparse
from mutagen import File
from datetime import timedelta

def get_audio_duration(file_path):
    """获取单个音频文件的时长（秒）"""
    try:
        audio = File(file_path)
        if audio is not None and hasattr(audio.info, 'length'):
            return audio.info.length
        return 0
    except Exception as e:
        print(f"无法读取文件 {file_path}：{e}")
        return 0

def calculate_total_duration(folder_path, extensions=None):
    """计算文件夹中所有音频文件的总时长"""
    if extensions is None:
        # 默认支持的音频格式
        extensions = ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a', '.wma']
    
    total_seconds = 0
    file_count = 0
    
    print(f"正在扫描文件夹：{folder_path}")
    
    for root, _, files in os.walk(folder_path):
        for file in files:
            file_path = os.path.join(root, file)
            file_ext = os.path.splitext(file)[1].lower()
            
            if file_ext in extensions:
                duration = get_audio_duration(file_path)
                if duration > 0:
                    total_seconds += duration
                    file_count += 1
                    print(f"文件：{file} - 时长：{timedelta(seconds=duration)}")
    
    return total_seconds, file_count

def format_time(seconds):
    """将秒数格式化为时:分:秒的格式"""
    td = timedelta(seconds=round(seconds))
    return str(td)

def main():
    # 在这里直接设置要扫描的文件夹路径
    folder_path = "D:/音乐文件夹"  # 请将此路径修改为您要扫描的文件夹
    
    # 可以直接在这里指定要扫描的文件扩展名，设为None则使用默认值
    extensions = None  # 例如：['.mp3', '.wav']
    
    total_seconds, file_count = calculate_total_duration(folder_path, extensions)
    
    print("\n统计结果:")
    print(f"共找到 {file_count} 个音频文件")
    print(f"总时长：{format_time(total_seconds)} (小时:分钟:秒)")
    print(f"总秒数：{total_seconds:.2f} 秒")

if __name__ == "__main__":
    main() 