import sqlite3
import time
import uuid
from flask import Flask, json, jsonify, make_response, request, send_file, render_template
import os
from datetime import datetime, timedelta
import threading
import queue
from pydub import AudioSegment
import librosa

# --- 配置 ---
DATABASE_PATH = '/home/<USER>/emotion2vec/data/json/session1000/tasks.db'
# 用于一次性数据导入的原始JSONL文件路径
INITIAL_DATA_JSON_PATH = "/home/<USER>/emotion2vec/data/json/session1000/session.jsonl"
# 以下路径现在是用于数据导出，而不是作为主要数据存储
SENSEVOICE_MARK_JSON_PATH = (
    "/home/<USER>/emotion2vec/data/json/session1000/sensevoice_marked_from_db.jsonl"
)
OTHER_JSON_PATH = "/home/<USER>/emotion2vec/data/json/session1000/other_marked_from_db.jsonl"
SEMANTIC_EMO_MARK_JSON_PATH = (
    "/home/<USER>/emotion2vec/data/json/session1000/sentantic_marked_from_db.jsonl"
)

TIMEOUT = 1200  # 任务锁定超时时间（秒），20分钟
REAL_TIME_EXPORT = True  # 启用实时导出功能

# --- 异步导出配置 ---
export_queue = queue.Queue()

app = Flask(__name__)

# --- 数据库设置和初始化 ---

def get_db_connection():
    """创建数据库连接。"""
    conn = sqlite3.connect(DATABASE_PATH, timeout=5, check_same_thread=False)
    conn.row_factory = sqlite3.Row  # 允许通过列名访问数据
    return conn

def init_db():
    """初始化数据库，创建表，并从JSONL导入数据。"""
    if os.path.exists(DATABASE_PATH):
        conn_check = get_db_connection()
        try:
            count = conn_check.execute('SELECT COUNT(id) FROM tasks').fetchone()[0]
            if count > 0:
                print("数据库已存在且包含数据，跳过初始化。")
                conn_check.close()
                return
        except sqlite3.OperationalError:
            # 表可能不存在，继续执行初始化
            pass
        conn_check.close()
    
    print("正在初始化数据库...")
    conn = get_db_connection()
    cursor = conn.cursor()

    # 创建表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            source TEXT NOT NULL UNIQUE,
            target TEXT,
            text_language TEXT,
            emo_target TEXT,
            event_target TEXT,
            source_len INTEGER,
            target_len INTEGER,
            with_or_wo_itn TEXT,
            status TEXT NOT NULL,
            locked_by TEXT,
            locked_at DATETIME,
            modified_at DATETIME,
            semantic_emo_target TEXT
        )
    ''')

    # 如果存在，则从JSONL文件导入数据
    if not os.path.exists(INITIAL_DATA_JSON_PATH):
        print(f"警告: 在 {INITIAL_DATA_JSON_PATH} 未找到初始数据文件，数据库将为空。")
        conn.commit()
        conn.close()
        return

    print(f"正在从 {INITIAL_DATA_JSON_PATH} 导入数据...")
    task_count = 0
    with open(INITIAL_DATA_JSON_PATH, 'r', encoding='utf-8') as f:
        for line in f:
            try:
                data = json.loads(line)
                cursor.execute('''
                    INSERT INTO tasks (source, target, text_language, emo_target, event_target, source_len, target_len, with_or_wo_itn, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending')
                ''', (
                    data.get('source'), data.get('target'), data.get('text_language'),
                    data.get('emo_target'), data.get('event_target'), data.get('source_len'),
                    data.get('target_len'), data.get('with_or_wo_itn')
                ))
                task_count += 1
            except json.JSONDecodeError:
                print(f"跳过无效的JSON行: {line.strip()}")
            except sqlite3.IntegrityError:
                print(f"跳过重复的数据源: {data.get('source')}")

    conn.commit()
    conn.close()
    print(f"数据库初始化成功，共导入 {task_count} 条任务。")

# --- 异步导出工作线程 ---
def export_worker():
    """从队列中获取任务并将其导出到文件。"""
    print("异步导出工作线程已启动。")
    conn = None
    while True:
        try:
            # 可以处理两种队列项：完整任务字典或任务ID
            item = export_queue.get()
            if item is None:  # 用于停止工作线程的信号
                break
            
            # 检查是否只收到了任务ID而不是完整的任务字典
            if isinstance(item, int) or isinstance(item, str) and item.isdigit():
                # 如果只收到ID，需要创建自己的数据库连接来获取任务
                try:
                    if conn is None:
                        conn = get_db_connection()
                    task = conn.execute('SELECT * FROM tasks WHERE id = ?', (item,)).fetchone()
                    if task:
                        task_dict = dict(task)
                        # 使用现有的导出函数处理单个任务
                        export_task_to_jsonl(task_dict)
                    else:
                        print(f"导出工作线程无法找到ID为{item}的任务")
                except sqlite3.Error as e:
                    print(f"导出工作线程数据库错误: {e}")
                    # 如果出现数据库错误，重新建立连接
                    if conn:
                        conn.close()
                    conn = get_db_connection()
            else:
                # 原有方式处理完整任务字典
                export_task_to_jsonl(item)

            export_queue.task_done()
        except Exception as e:
            print(f"异步导出工作线程出现错误: {e}")
        
        # 如果队列为空，考虑关闭数据库连接以释放资源
        if export_queue.empty() and conn is not None:
            conn.close()
            conn = None

# --- 实时导出功能 ---

def export_task_to_jsonl(task_dict):
    """实时导出单个任务到相应的JSONL文件，根据指定格式"""
    try:
        # 如果target为空，跳过导出
        if not task_dict.get('target', '').strip():
            print(f"ID为{task_dict.get('id')}的任务文本为空，跳过导出")
            return True
            
        status = task_dict.get('status')
        source_path = task_dict.get('source', '')
        # 1. 根据要求修改key的格式
        key = f"mark_{os.path.basename(source_path)}" if source_path else ''
        
        # 2. 提取 semantic_emo_target 单独处理
        semantic_emo_target = task_dict.get('semantic_emo_target')

        # 构造基础的导出数据，不包含 semantic_emo_target
        base_export_data = {
            "emo_target": task_dict.get('emo_target', ''),
            "event_target": task_dict.get('event_target', ''),
            "key": key,
            "source": task_dict.get('source', ''),
            "source_len": task_dict.get('source_len', 0),
            "target": task_dict.get('target', ''),
            "target_len": task_dict.get('target_len', 0),
            "text_language": task_dict.get('text_language', ''),
            "with_or_wo_itn": task_dict.get('with_or_wo_itn')
        }
        
        if status == 'other':
            # 对于 'other' 任务，我们仍然包含所有信息
            other_export_data = base_export_data.copy()
            if semantic_emo_target:
                other_export_data["semantic_emo_target"] = semantic_emo_target
            
            with open(OTHER_JSON_PATH, 'a', encoding='utf-8') as f:
                f.write(json.dumps(other_export_data, ensure_ascii=False) + '\n')
            print(f"已实时导出ID为{task_dict.get('id')}的other任务")
        
        elif status == 'completed':
            # 1. 导出到 SENSEVOICE 文件（不含 semantic_emo_target）
            with open(SENSEVOICE_MARK_JSON_PATH, 'a', encoding='utf-8') as f_sv:
                f_sv.write(json.dumps(base_export_data, ensure_ascii=False) + '\n')
            
            # 2. 如果存在，单独导出到 SEMANTIC 文件
            if semantic_emo_target:
                semantic_data = {
                    "sentence": base_export_data.get('target'),
                    "semantic_emo_target": semantic_emo_target,
                }
                with open(SEMANTIC_EMO_MARK_JSON_PATH, 'a', encoding='utf-8') as f_sem:
                    f_sem.write(json.dumps(semantic_data, ensure_ascii=False) + '\n')
                    
            print(f"已实时导出ID为{task_dict.get('id')}的completed任务")
        
        return True
    except Exception as e:
        print(f"实时导出任务失败: {e}")
        return False

# --- Flask 路由 ---

@app.route("/vvue")
def index():
    """提供主页面并通过cookie分配用户ID。"""
    user_id = request.cookies.get("user_id")
    if not user_id:
        user_id = str(uuid.uuid4())
    # 确保您的模板文件名正确
    response = make_response(render_template("index_emo_crop.vue"))
    response.set_cookie("user_id", user_id, max_age=60 * 60 * 24 * 30)
    return response


@app.route("/get_file", methods=["GET"])
def get_file():
    """
    为用户获取一个任务。
    优先返回已被该用户锁定的任务。
    如果没有，则以原子方式分配一个新的'pending'状态的任务。
    此操作现在对并发是安全的。
    """
    user_id = request.args.get("user_id")
    if not user_id:
        return jsonify(message="需要提供用户ID。", code=400), 400

    conn = None
    try:
        conn = get_db_connection()
        
        # 1. 检查用户是否已有锁定的任务（例如页面刷新）
        task = conn.execute(
            'SELECT * FROM tasks WHERE locked_by = ? AND status = ?', (user_id, 'locked')
        ).fetchone()

        if task:
            result = jsonify(json_data=dict(task))
            conn.close()
            return result

        # 2. 如果没有，尝试以原子方式查找并锁定一个新任务
        with conn:
            # 这个单一的UPDATE语句会原子性地找到第一个待处理任务并锁定它，
            # 从而防止了"先SELECT后UPDATE"的竞态条件。
            cursor = conn.execute(
                """UPDATE tasks
                   SET status = 'locked', locked_by = ?, locked_at = ?
                   WHERE id = (SELECT id FROM tasks WHERE status = 'pending' LIMIT 1)""",
                (user_id, datetime.now())
            )

            # 如果rowcount为0，表示子查询没有找到任何'pending'的任务
            if cursor.rowcount == 0:
                conn.close()
                return jsonify(message="所有数据已处理完毕，没有更多数据需要标注。")
        
        # 3. 任务已为我们锁定，现在找出它具体是哪个任务
        locked_task = conn.execute(
            'SELECT * FROM tasks WHERE locked_by = ? AND status = ?', (user_id, 'locked')
        ).fetchone()
        
        if locked_task:
            conn.close()
            return jsonify(json_data=dict(locked_task))
        else:
            # 这种情况不太可能发生，但作为保障
            conn.close()
            return jsonify(message="无法获取锁定的任务，请重试。", code=500), 500

    except sqlite3.OperationalError as e:
        # 捕获特定的"数据库锁定"错误，这是高并发下的常见问题
        if "database is locked" in str(e):
            print(f"数据库锁定 in /get_file: {e}")
            if conn:
                conn.close()
            return jsonify(message="服务器正忙，数据库锁定，请稍后重试。", code=503), 503
        else:
            print(f"数据库操作错误 in /get_file: {e}")
            if conn:
                conn.close()
            return jsonify(message="数据库操作错误，请稍后重试。", code=500), 500
    except sqlite3.Error as e:
        print(f"数据库错误 in /get_file: {e}")
        if conn:
            conn.close()
        return jsonify(message="数据库错误，请稍后重试。", code=500), 500
    except Exception as e:
        print(f"未预期的错误 in /get_file: {e}")
        if conn:
            conn.close()
        return jsonify(message="服务器发生错误，请稍后重试。", code=500), 500


@app.route("/modify_file", methods=["POST"])
def modify_file():
    """
    处理用户提交的已完成任务。
    原子性地更新数据库，并实时导出到JSONL文件。
    """
    data = request.json
    user_id = data.get("user_id")
    task_id = data.get("task_id") 
    modified_data = data.get("json_content")

    if not all([user_id, task_id, modified_data]):
        return jsonify(message="缺少 user_id, task_id, 或 json_content。", code=400), 400

    conn = get_db_connection()

    # 验证该任务当前是否由此用户锁定
    task = conn.execute(
        'SELECT * FROM tasks WHERE id = ? AND locked_by = ? AND status = ?',
        (task_id, user_id, 'locked')
    ).fetchone()

    if not task:
        conn.close()
        return jsonify(message="提交失败，任务无效或已被他人锁定，请刷新获取新数据。", code=403), 403
    
    # 检查是否超时
    if (datetime.now() - datetime.fromisoformat(task['locked_at'])).seconds > TIMEOUT:
        conn.close()
        return jsonify(message="提交失败，任务已超时，请刷新获取新数据。", code=408), 408

    # 用新数据更新任务
    new_status = 'completed'
    if modified_data.get("emo_target") == "<|OTHER|>" or modified_data.get("semantic_emo_target") == "<|OTHER|>":
        new_status = 'other'

    try:
        with conn:
            conn.execute(
                '''UPDATE tasks SET 
                   target = ?, 
                   target_len = ?,
                   semantic_emo_target = ?, 
                   emo_target = ?,
                   event_target = ?,
                   text_language = ?,
                   status = ?, 
                   modified_at = ?,
                   locked_by = NULL,
                   locked_at = NULL
                   WHERE id = ?''',
                (
                    modified_data.get('target'),
                    len(modified_data.get('target', '').replace(" ", "")),
                    modified_data.get('semantic_emo_target'),
                    modified_data.get('emo_target'),
                    modified_data.get('event_target'),
                    modified_data.get('text_language'),
                    new_status,
                    datetime.now(),
                    task_id
                )
            )
        
        # 改进的实时导出功能 - 只将任务ID放入队列，完全异步处理
        if REAL_TIME_EXPORT:
            # 不再查询数据库，直接将任务ID放入队列，让导出工作线程自己查询
            export_queue.put(task_id)
        
        return jsonify(message="文件修改成功。", code=200)
    except sqlite3.Error as e:
        print(f"数据库更新错误 in /modify_file: {e}")
        return jsonify(message="数据库更新时出错。", code=500), 500
    finally:
        conn.close()

def cleanup_timed_out_tasks():
    """定期检查锁定超时的任务并重置它们。"""
    while True:
        time.sleep(600) # 每10分钟检查一次
        try:
            print("正在清理超时任务...")
            conn = get_db_connection()
            timeout_threshold = datetime.now() - timedelta(seconds=TIMEOUT)
            
            with conn:
                tasks_to_reset = conn.execute(
                    "SELECT id FROM tasks WHERE status = 'locked' AND locked_at < ?",
                    (timeout_threshold,)
                ).fetchall()
                
                if tasks_to_reset:
                    task_ids = [(task['id'],) for task in tasks_to_reset]
                    print(f"发现 {len(task_ids)} 个超时任务需要重置。")
                    conn.executemany(
                        "UPDATE tasks SET status = 'pending', locked_by = NULL, locked_at = NULL WHERE id = ?",
                        task_ids
                    )
        except sqlite3.Error as e:
            print(f"清理任务时出错: {e}")
        finally:
            if conn:
                conn.close()

@app.route("/get_total", methods=["GET"])
def get_total():
    """获取待处理任务的总数。"""
    conn = get_db_connection()
    total = conn.execute("SELECT COUNT(id) FROM tasks WHERE status = 'pending'").fetchone()[0]
    conn.close()
    return jsonify(total_len=total)

@app.route("/get_completedFiles", methods=["GET"])
def get_completedFiles():
    """获取已完成任务的总数。"""
    conn = get_db_connection()
    total = conn.execute("SELECT COUNT(id) FROM tasks WHERE status IN ('completed', 'other')").fetchone()[0]
    conn.close()
    return jsonify(total_len=total)

@app.route("/audio/<path:filename>", methods=["GET"])
def serve_audio(filename):
    """提供音频文件。此部分不变。"""
    print(filename)
    full_path = "/" + filename
    return send_file(full_path)


@app.route("/export_to_jsonl", methods=["POST"])
def export_to_jsonl():
    """导出功能，将所有完成的数据导出回JSONL文件。"""
    print("正在将已完成任务导出到JSONL文件...")
    conn = get_db_connection()
    
    # 清空原有文件
    for path in [OTHER_JSON_PATH, SENSEVOICE_MARK_JSON_PATH, SEMANTIC_EMO_MARK_JSON_PATH]:
        with open(path, 'w', encoding='utf-8') as f:
            pass
    
    # 导出其他任务
    with open(OTHER_JSON_PATH, 'w', encoding='utf-8') as f:
        for task in conn.execute("SELECT * FROM tasks WHERE status = 'other'").fetchall():
            task_dict = dict(task)
            
            # 如果target为空，跳过导出
            if not task_dict.get('target', '').strip():
                print(f"ID为{task_dict.get('id')}的任务文本为空，跳过导出")
                continue
                
            source_path = task_dict.get('source', '')
            key = f"mark_{os.path.basename(source_path)}" if source_path else ''
            
            export_data = {
                "emo_target": task_dict.get('emo_target', ''),
                "event_target": task_dict.get('event_target', ''),
                "key": key,
                "source": task_dict.get('source', ''),
                "source_len": task_dict.get('source_len', 0),
                "target": task_dict.get('target', ''),
                "target_len": task_dict.get('target_len', 0),
                "text_language": task_dict.get('text_language', ''),
                "with_or_wo_itn": task_dict.get('with_or_wo_itn')
            }
            if task_dict.get('semantic_emo_target'):
                export_data["semantic_emo_target"] = task_dict.get('semantic_emo_target')
                
            f.write(json.dumps(export_data, ensure_ascii=False) + '\n')
    
    # 导出completed任务
    with open(SENSEVOICE_MARK_JSON_PATH, 'w', encoding='utf-8') as f_sv, \
         open(SEMANTIC_EMO_MARK_JSON_PATH, 'w', encoding='utf-8') as f_sem:
        for task in conn.execute("SELECT * FROM tasks WHERE status = 'completed'").fetchall():
            task_dict = dict(task)
            
            # 如果target为空，跳过导出
            if not task_dict.get('target', '').strip():
                print(f"ID为{task_dict.get('id')}的任务文本为空，跳过导出")
                continue
                
            semantic_emo_target = task_dict.get('semantic_emo_target')
            
            source_path = task_dict.get('source', '')
            key = f"mark_{os.path.basename(source_path)}" if source_path else ''
            
            # 按照新格式构造数据，不包含 semantic_emo_target
            export_data = {
                "emo_target": task_dict.get('emo_target', ''),
                "event_target": task_dict.get('event_target', ''),
                "key": key,
                "source": task_dict.get('source', ''),
                "source_len": task_dict.get('source_len', 0),
                "target": task_dict.get('target', ''),
                "target_len": task_dict.get('target_len', 0),
                "text_language": task_dict.get('text_language', ''),
                "with_or_wo_itn": task_dict.get('with_or_wo_itn')
            }
                
            f_sv.write(json.dumps(export_data, ensure_ascii=False) + '\n')
            
            # 如果存在，单独写入 semantic 文件
            if semantic_emo_target:
                f_sem.write(json.dumps({
                    "sentence": task_dict.get('target'),
                    "semantic_emo_target": semantic_emo_target,
                }, ensure_ascii=False) + '\n')
                
    conn.close()
    return jsonify(message=f"数据导出成功。")


@app.route("/save_new_task", methods=["POST"])
def save_new_task():
    """处理用户提交的新增任务。
    复制现有任务并将其作为新任务保存到数据库中，
    同时处理修改过的路径（添加后缀）和其他元数据。
    """
    data = request.json
    user_id = data.get("user_id")
    task_id = data.get("task_id")  # 原始任务ID
    modified_data = data.get("json_content")  # 修改后的数据
    audio_suffix = data.get("audio_suffix")  # 新音频后缀
    
    if not all([user_id, task_id, modified_data, audio_suffix]):
        return jsonify(message="缺少 user_id, task_id, json_content 或 audio_suffix。", code=400), 400
    
    conn = get_db_connection()
    
    # 1. 验证并获取原始任务
    original_task = conn.execute(
        'SELECT * FROM tasks WHERE id = ?',
        (task_id,)
    ).fetchone()

    if not original_task:
        conn.close()
        return jsonify(message="提交失败，原始任务不存在。", code=404), 404
    
    # 2. 处理音频文件和路径
    source_path = modified_data.get('source', '')
    new_source_path = source_path
    # 初始 source_len 来自前端，如果未裁剪则为原始值
    new_source_len = modified_data.get('source_len', 0)

    if source_path:
        filename, ext = os.path.splitext(source_path)
        new_source_path = f"{filename}_{audio_suffix}{ext}"
        
        absolute_path = f"/{source_path}"
        new_absolute_path = f"/{new_source_path}"
        
        if not os.path.exists(absolute_path):
            conn.close()
            return jsonify(message=f"源音频文件不存在: {absolute_path}", code=404), 404
        
        try:
            # 复制音频文件到新路径
            os.makedirs(os.path.dirname(new_absolute_path), exist_ok=True)
            if os.path.exists(new_absolute_path):
                os.remove(new_absolute_path)
            import shutil
            shutil.copy2(absolute_path, new_absolute_path)
            
            # 关键：为新文件重新计算 source_len
            new_source_len = calculate_source_len_from_fbank(new_absolute_path)

        except Exception as e:
            conn.close()
            return jsonify(message=f"创建新音频文件失败: {str(e)}", code=500), 500
    
    # 3. 准备新任务的数据
    # 从页面获取 emo, event, text, language
    target_text = modified_data.get('target', '')
    target_len = len(target_text.replace(" ", ""))
    semantic_emo_target = modified_data.get('semantic_emo_target', '')
    emo_target = modified_data.get('emo_target')
    event_target = modified_data.get('event_target')
    text_language = modified_data.get('text_language')

    # 关键：从源数据获取 with_or_wo_itn
    with_or_wo_itn = original_task['with_or_wo_itn']

    # 决定任务状态
    new_status = 'completed'
    if emo_target == "<|OTHER|>" or semantic_emo_target == "<|OTHER|>":
        new_status = 'other'
        
    try:
        with conn:
            # 插入新任务
            cursor = conn.execute(
                '''INSERT INTO tasks 
                   (source, target, target_len, semantic_emo_target, emo_target, 
                    event_target, text_language, with_or_wo_itn, status, modified_at, source_len)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                (
                    new_source_path,         # 更新的路径
                    target_text,
                    target_len,              # 更新的 target_len
                    semantic_emo_target,
                    emo_target,
                    event_target,
                    text_language,
                    with_or_wo_itn,          # 从源数据获取
                    new_status,
                    datetime.now(),
                    new_source_len           # 更新的 source_len
                )
            )
            
            new_task_id = cursor.lastrowid
            
        # 如果启用了实时导出，也将新任务加入导出队列
        if REAL_TIME_EXPORT and new_task_id:
            export_queue.put(new_task_id)
            
        return jsonify(message="新任务创建成功。", code=200)
    except sqlite3.IntegrityError as e:
        # source 必须是唯一的，带后缀的文件名可能已存在于数据库中
        print(f"数据库操作错误 in /save_new_task: {e}")
        return jsonify(message=f"数据库更新时出错: {str(e)}。可能原因：带有后缀 '{audio_suffix}' 的文件已存在于数据库中。", code=500), 500
    except sqlite3.Error as e:
        print(f"数据库操作错误 in /save_new_task: {e}")
        return jsonify(message=f"数据库更新时出错: {str(e)}", code=500), 500
    finally:
        conn.close()


# 启动时执行一次完整导出
def initial_export_all():
    """程序启动时将所有已完成任务放入导出队列。"""
    if REAL_TIME_EXPORT:
        print("程序启动时，准备初始数据进行异步导出...")
        
        # 清空现有文件
        for path in [OTHER_JSON_PATH, SENSEVOICE_MARK_JSON_PATH, SEMANTIC_EMO_MARK_JSON_PATH]:
            if os.path.exists(path):
                open(path, 'w', encoding='utf-8').close()
                print(f"已清空文件: {path}")
        
        # 将所有已完成和other状态的任务放入队列
        conn = get_db_connection()
        try:
            tasks_to_export = conn.execute("SELECT * FROM tasks WHERE status IN ('completed', 'other')").fetchall()
            print(f"发现 {len(tasks_to_export)} 个历史任务需要导出。")
            for task in tasks_to_export:
                export_queue.put(dict(task))
            print("所有历史任务已加入异步导出队列。")
        except Exception as e:
            print(f"初始数据导出准备失败: {e}")
        finally:
            conn.close()

def calculate_source_len_from_fbank(file_path):
    """
    通过计算音频的fbank特征帧数来获取source_len。
    """
    try:
        y, sr = librosa.load(file_path, sr=None)

        # 设置 fbank 特征参数
        n_fft = 2048  # FFT窗口大小
        hop_length = 512  # 每帧之间的间隔
        n_mels = 128  # mel频率的数量

        # 计算 fbank 特征
        fbank = librosa.feature.melspectrogram(y=y, sr=sr, n_fft=n_fft, hop_length=hop_length, n_mels=n_mels)

        # 获取 fbank 帧数
        source_len = fbank.shape[1]
        return source_len
    except Exception as e:
        print(f"为 {file_path} 计算 fbank 时出错: {e}")
        return 0

@app.route('/crop_audio', methods=['POST'])
def crop_audio():
    """
    裁剪音频文件并将其保存到新位置。
    如果不是为新任务裁剪，则更新数据库中的现有记录。
    支持单个或多个裁剪区域，并会自动合并重叠的区域。
    """
    data = request.json
    source = data.get('source')
    task_id = data.get('task_id')
    regions = data.get('regions')
    is_new_task = data.get('is_new_task', False) # 新增标志，判断是否为新任务

    if not source or not task_id:
        return jsonify(message="缺少 source 或 task_id", code=400), 400

    # 验证输入参数
    if not regions and (start_time is None or end_time is None):
        return jsonify(message="必须提供 regions 列表或 start_time 和 end_time", code=400), 400
    
    if regions and (not isinstance(regions, list) or not regions):
        return jsonify(message="regions 必须是一个非空列表", code=400), 400

    # 原始音频文件的绝对路径
    original_audio_path = f"/{source}"

    if not os.path.exists(original_audio_path):
        return jsonify(message=f"文件不存在: {original_audio_path}", code=404), 404

    try:
        # 加载音频
        audio = AudioSegment.from_file(original_audio_path)
        
        cropped_audio = AudioSegment.empty()

        if regions:
            # --- 新增：合并重叠区域 ---
            # 1. 按起始时间排序
            sorted_regions = sorted(regions, key=lambda r: r.get('start_time', 0))
            
            merged_regions = []
            if sorted_regions:
                # 2. 初始化合并列表
                current_merge = sorted_regions[0]

                # 3. 遍历并合并
                for next_region in sorted_regions[1:]:
                    # 如果下一个区域与当前合并区域重叠或连续
                    if next_region['start_time'] <= current_merge['end_time']:
                        # 扩展当前合并区域的结束时间
                        current_merge['end_time'] = max(current_merge['end_time'], next_region['end_time'])
                    else:
                        # 没有重叠，将当前合并好的区域存入列表，并开始新的合并
                        merged_regions.append(current_merge)
                        current_merge = next_region
                
                # 不要忘记添加最后一个合并区域
                merged_regions.append(current_merge)
            # --- 合并结束 ---

            # 使用合并后的区域进行裁剪
            for region in merged_regions:
                start_ms = float(region['start_time']) * 1000
                end_ms = float(region['end_time']) * 1000
                cropped_audio += audio[start_ms:end_ms]
        else:
            # 旧版：处理单个区域
            start_ms = float(start_time) * 1000
            end_ms = float(end_time) * 1000
            cropped_audio = audio[start_ms:end_ms]
        
        if len(cropped_audio) == 0:
            return jsonify(message="裁剪后的音频长度为0，请检查裁剪区域。", code=400), 400

        # --- 创建新路径 ---
        # 获取原始文件的目录和基本名称
        original_dir = os.path.dirname(source)
        original_basename = os.path.basename(source)

        # 定义新的 'cropped' 子目录
        cropped_dir_name = "cropped"
        new_dir_path_relative = os.path.join(original_dir, cropped_dir_name)
        new_dir_path_absolute = f"/{new_dir_path_relative}"

        # 如果新目录不存在，则创建它
        os.makedirs(new_dir_path_absolute, exist_ok=True)
        
        # 定义新的文件路径（相对和绝对）
        new_source_path_relative = os.path.join(new_dir_path_relative, original_basename)
        new_audio_path_absolute = f"/{new_source_path_relative}"
        
        # 导出裁剪后的音频到新路径
        file_format = os.path.splitext(original_basename)[1][1:] or "wav"
        cropped_audio.export(new_audio_path_absolute, format=file_format)
        
        # 为新文件计算 source_len
        new_source_len = calculate_source_len_from_fbank(new_audio_path_absolute)

        # 如果不是创建独立任务，则更新原始数据库记录
        if not is_new_task:
            conn = get_db_connection()
            with conn:
                conn.execute(
                    'UPDATE tasks SET source = ?, source_len = ? WHERE id = ?',
                    (new_source_path_relative, new_source_len, task_id)
                )
            conn.close()

        # 返回成功消息和新路径及新长度
        return jsonify(
            message="音频裁剪并保存到新位置成功",
            new_source=new_source_path_relative,
            new_source_len=new_source_len, # 返回新计算的长度
            new_duration=len(cropped_audio) / 1000.0,
            code=200
        )

    except Exception as e:
        print(f"裁剪音频时出错: {e}")
        return jsonify(message=f"裁剪失败: {e}", code=500), 500

@app.route("/get_created_tasks", methods=["GET"])
def get_created_tasks():
    """获取所有已创建的任务列表，包括任务的基本信息。"""
    conn = None
    try:
        conn = get_db_connection()
        # 查询所有已完成和other状态的任务
        tasks = conn.execute(
            'SELECT id, source, target, text_language, emo_target, event_target, status FROM tasks WHERE status IN ("completed", "other") ORDER BY id DESC'
        ).fetchall()
        
        # 转换为字典列表
        task_list = [dict(task) for task in tasks]
        
        return jsonify(tasks=task_list)
    except sqlite3.Error as e:
        print(f"数据库错误 in /get_created_tasks: {e}")
        return jsonify(message=f"获取任务列表失败: {str(e)}", code=500), 500
    finally:
        if conn:
            conn.close()


@app.route("/get_task_detail", methods=["GET"])
def get_task_detail():
    """根据任务ID获取任务的详细信息。"""
    task_id = request.args.get("task_id")
    if not task_id:
        return jsonify(message="缺少任务ID", code=400), 400
        
    conn = None
    try:
        conn = get_db_connection()
        # 查询指定任务的所有信息
        task = conn.execute(
            'SELECT * FROM tasks WHERE id = ?',
            (task_id,)
        ).fetchone()
        
        if not task:
            return jsonify(message="未找到指定任务", code=404), 404
            
        # 转换为字典
        task_dict = dict(task)
        
        return jsonify(task=task_dict)
    except sqlite3.Error as e:
        print(f"数据库错误 in /get_task_detail: {e}")
        return jsonify(message=f"获取任务详情失败: {str(e)}", code=500), 500
    finally:
        if conn:
            conn.close()


@app.route("/update_task", methods=["POST"])
def update_task():
    """更新已存在的任务，用于修改已创建任务的信息。"""
    data = request.json
    user_id = data.get("user_id")
    task_id = data.get("task_id") 
    modified_data = data.get("json_content")

    if not all([user_id, task_id, modified_data]):
        return jsonify(message="缺少 user_id, task_id, 或 json_content。", code=400), 400

    conn = get_db_connection()

    # 检查任务是否存在
    task = conn.execute(
        'SELECT * FROM tasks WHERE id = ?',
        (task_id,)
    ).fetchone()

    if not task:
        conn.close()
        return jsonify(message="任务不存在", code=404), 404
    
    # 决定任务状态
    new_status = 'completed'
    if modified_data.get("emo_target") == "<|OTHER|>" or modified_data.get("semantic_emo_target") == "<|OTHER|>":
        new_status = 'other'

    try:
        with conn:
            conn.execute(
                '''UPDATE tasks SET 
                   target = ?, 
                   target_len = ?,
                   semantic_emo_target = ?, 
                   emo_target = ?,
                   event_target = ?,
                   text_language = ?,
                   status = ?, 
                   modified_at = ?
                   WHERE id = ?''',
                (
                    modified_data.get('target'),
                    len(modified_data.get('target', '').replace(" ", "")),
                    modified_data.get('semantic_emo_target', ''),
                    modified_data.get('emo_target'),
                    modified_data.get('event_target'),
                    modified_data.get('text_language'),
                    new_status,
                    datetime.now(),
                    task_id
                )
            )
        
        # 更新后将任务放入导出队列
        if REAL_TIME_EXPORT:
            export_queue.put(task_id)
        
        return jsonify(message="任务更新成功。", code=200)
    except sqlite3.Error as e:
        print(f"数据库更新错误 in /update_task: {e}")
        return jsonify(message=f"更新任务失败: {str(e)}", code=500), 500
    finally:
        conn.close()


if __name__ == "__main__":
    init_db()
    
    # 启动异步导出工作线程
    export_thread = threading.Thread(target=export_worker, daemon=True)
    export_thread.start()
    
    # 程序启动时进行一次完整导出（放入队列）
    initial_export_all()
    
    cleanup_thread = threading.Thread(target=cleanup_timed_out_tasks, daemon=True)
    cleanup_thread.start()
    
    # 现在可以安全地以多线程模式运行，以获得更好的并发性能
    app.run(host="0.0.0.0", port=5003, threaded=True) 