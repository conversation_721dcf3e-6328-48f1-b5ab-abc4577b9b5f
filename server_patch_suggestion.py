"""
服务器端代码修改建议 - 添加文件锁机制

此文件提供了针对dataserver_voice_mark.py的修改建议，以解决并发访问时的文件读写问题。
主要添加了文件锁机制，确保在多用户并发访问时文件读写的原子性和一致性。
"""

import time
import uuid
import threading
from flask import (
    Flask,
    json,
    jsonify,
    make_response,
    request,
    send_file,
    send_from_directory,
    render_template,
)
import os

app = Flask(__name__)

# 定义文件路径
JSON_PATH = "/home/<USER>/emotion2vec/data/json/session1213/session.jsonl"
SENSEVOICE_MARK_JSON_PATH = (
    "/home/<USER>/emotion2vec/data/json/session1213/sensevoice_marked.jsonl"
)
OTHER_JSON_PATH = "/home/<USER>/emotion2vec/data/json/session1213/other_marked.jsonl"
SEMANTIC_EMO_MARK_JSON_PATH = (
    "/home/<USER>/emotion2vec/data/json/session1213/sentantic_marked.jsonl"
)

# 创建文件锁对象
json_file_lock = threading.RLock()
sensevoice_file_lock = threading.RLock() 
other_file_lock = threading.RLock()
semantic_file_lock = threading.RLock()

# 数据分配状态锁
data_status_lock = threading.Lock()
data_status = {}  # {key: {'user': None, 'timestamp': None}}
TIMEOUT = 1200  # 数据超时时间（秒）

@app.route("/get_file", methods=["GET"])
def get_file():
    global current_file, total_len
    user_id = request.args.get("user_id")
    print(f"用户 {user_id} 请求获取文件")
    
    if current_file is None:
        current_file = JSON_PATH
    
    # 使用文件锁确保文件读取的原子性
    with json_file_lock:
        try:
            with open(current_file, "r", encoding="utf-8") as f:
                lines = f.readlines()
            
            if not lines:
                return jsonify(message="没有更多文件可修改。")
                
            total_len = len(lines)
            
            # 查找可分配给当前用户的数据
            from datetime import datetime
            
            for line in lines:
                try:
                    json_data = json.loads(line.strip())
                    key = json_data["source"]  # 假设每条数据有唯一 key
                    
                    with data_status_lock:
                        # 检查数据是否已分配，若未分配或超时则分配给当前用户
                        if (
                            key not in data_status
                            or data_status[key]["user"] is None
                            or (datetime.now() - data_status[key]["timestamp"]).seconds > TIMEOUT
                        ):
                            data_status[key] = {"user": user_id, "timestamp": datetime.now()}
                            return jsonify(json_data=json_data)
                        elif user_id == data_status[key]["user"]:
                            # 更新时间戳
                            data_status[key]["timestamp"] = datetime.now()
                            return jsonify(json_data=json_data)
                except json.JSONDecodeError as e:
                    # 记录错误但继续处理下一行
                    print(f"JSON解析错误: {e}")
                    continue
                    
            return jsonify(message="所有数据已被分配，请稍后再试。")
        except Exception as e:
            print(f"获取文件时出错: {e}")
            return jsonify(message="服务器内部错误，请稍后再试。"), 500


@app.route("/modify_file", methods=["POST"])
def modify_file():
    try:
        data = request.json
        user_id = data["user_id"]
        modified_audio_info = data["json_content"]
        json_object = json.loads(modified_audio_info)
        key = json_object["source"]
        
        # 检查数据归属和超时
        with data_status_lock:
            from datetime import datetime
            if (
                key not in data_status
                or data_status[key]["user"] != user_id
                or (datetime.now() - data_status[key]["timestamp"]).seconds > TIMEOUT
            ):
                return jsonify(
                    message="提交失败，数据无效或已超时，请刷新获取新数据。", code=500
                )
        
        # 根据标注内容选择不同的输出文件
        target_len = json_object.get("target_len", 0)
        if target_len != 0:
            if json_object["emo_target"] == "<|OTHER|>":
                # 使用文件锁保护写入操作
                with other_file_lock:
                    with open(OTHER_JSON_PATH, "a", encoding="utf-8") as f:
                        f.write(json.dumps(json_object, ensure_ascii=False) + "\n")
            else:
                with sensevoice_file_lock:
                    with open(SENSEVOICE_MARK_JSON_PATH, "a", encoding="utf-8") as f:
                        f.write(json.dumps(json_object, ensure_ascii=False) + "\n")
        
        # 从源文件中删除已处理的数据，使用文件锁保护读写操作
        with json_file_lock:
            # 读取当前文件内容
            remaining_lines = []
            try:
                with open(JSON_PATH, "r", encoding="utf-8") as f:
                    for line in f:
                        try:
                            audio_info = json.loads(line.strip())
                            if str(audio_info["source"]) != json_object["source"]:
                                remaining_lines.append(line)
                        except json.JSONDecodeError:
                            # 如果某行解析错误，仍保留该行
                            remaining_lines.append(line)
                            
                # 写回剩余内容
                with open(JSON_PATH, "w", encoding="utf-8") as f:
                    f.writelines(remaining_lines)
            except Exception as e:
                print(f"修改文件时出错: {e}")
                return jsonify(message=f"服务器内部错误: {str(e)}", code=500)
        
        # 释放数据状态
        with data_status_lock:
            if key in data_status:
                data_status[key]["user"] = None
                
        return jsonify(message="文件修改成功。", code=200)
        
    except Exception as e:
        print(f"处理提交请求时出错: {e}")
        return jsonify(message=f"服务器内部错误: {str(e)}", code=500)

# 其他函数保持不变...

# 启动测试命令举例
if __name__ == "__main__":
    app.run(host="0.0.0.0", port=6002, threaded=True) 