前置要求：

- 将大量的音频保存至一个目录下

训练步骤：

1. 将大量音频进行小段切分

   - 首先先打开/home/<USER>/emotion2vec/SenseVoice/wav2json_data.py，对main代码处修改变量

     ```python
         video_dir = "/home/<USER>/下载/video_1213_perfinetrain  
         mp3_dir = "/home/<USER>/emotion2vec/data/mp3/session1213" #修改为待切分mp3音频路径
         temp_dir = "/home/<USER>/emotion2vec/data/mp3/temp" #不动
         output_json_path = "/home/<USER>/emotion2vec/data/json/session1213/session.jsonl" #修改为解析后数据的保存路径
     ```
     
   - 运行/home/<USER>/emotion2vec/SenseVoice/wav2json_data.py，得到解析后的路径： output_json_path 
   
2. 数据标注

   - 打开/home/<USER>/emotion2vec/dataserver/dataserver_voice_mark.py，修改路径变量

     ```python
     JSON_PATH = "/home/<USER>/emotion2vec/data/json/session1213/session.jsonl" #解析原数据路径
     SENSEVOICE_MARK_JSON_PATH = (
       "/home/<USER>/emotion2vec/data/json/session1213/sensevoice_marked.jsonl"#标注后保存的数据
     )
     OTHER_JSON_PATH = "/home/<USER>/emotion2vec/data/json/session1213/other_marked.jsonl"#情感为“其他”的数据
     ```

3. 训练

   1. 将总数据按一定比例

   2.  按需求修改 finetune.sh 

      ```bash
      
      #基础模型
      model_name_or_model_dir="iic/SenseVoiceSmall"
      
      #训练数据路径
      train_data=/home/<USER>/emotion2vec/data/json/alldata/Voice/Voice_All_Train_Data_rm.jsonl #400
      val_data=/home/<USER>/emotion2vec/data/json/alldata/Voice/Voice_All_Val_Data_rm.jsonl
      
      
      #模型保存路径
      output_dir="./outputs_250209"
      
      #训练日志路径
      log_file="${output_dir}/log.txt"
      
      #训练轮次
      ++train_conf.max_epoch=80\
      
      #学习率
      ++optim_conf.lr=0.0002 \
      
      #训练时保存N个性能最好的模型
      ++train_conf.keep_nbest_models=5 
      ```
      
      
      
   2.  运行finetune.sh
   
      ```bash
      bash /home/<USER>/emotion2vec/SenseVoice/finetune.sh
      ```
      
      
------
可选
4. 量化：导出并缩小模型大小

   ```bash
   funasr-export ++model=“modelPath” ++quantize=true
   ```

5. 训练过程可视化

   ```bash
   tensorboard :tensorboard --logdir="./outputs/2024-10-25"
   ```

6. 效果评测

   1. 打开/home/<USER>/emotion2vec/SenseVoice/webui copy 3.py

   2. 修改模型路径与端口

      ```python
      model_dir1 = "iic/SenseVoiceSmall" #模型A路径
      model_dir2 = "/home/<USER>/emotion2vec/SenseVoice/outputs_241122" #模型B路径
      
      demo.launch(share=True, server_name='0.0.0.0', ssl_certfile="/home/<USER>/emotion2vec/cert.pem", 
                      ssl_keyfile="/home/<USER>/emotion2vec/key.pem", ssl_verify=False, server_port=8094)#server_port为服务器端口，通过公网IP:端口即可访问
      ```

      



