import time
import uuid
import json
import random
from locust import HttpUser, task, between


class AudioMarkUser(HttpUser):
    wait_time = between(1, 3)  # 用户在每个任务之间等待1-3秒
    user_id = None

    def on_start(self):
        # 首次访问，获取user_id
        self.user_id = str(uuid.uuid4())
        # 访问主页获取cookie
        response = self.client.get("/vvue", name="访问主页")
        # 查看进度信息
        self.client.get("/get_total", name="获取总任务数")
        self.client.get("/get_completedFiles", name="获取已完成任务数")

    @task(4)
    def complete_mark_task(self):
        """完整的标注任务流程：获取任务->处理任务->提交任务"""
        # 1. 获取待标注文件
        response = self.client.get(f"/get_file?user_id={self.user_id}", name="获取标注任务")
        if "json_data" not in response.json():
            return

        json_data = response.json()["json_data"]

        # 2. 获取音频文件
        if "source" in json_data:
            audio_url = f"/audio/{json_data['source']}"
            self.client.get(audio_url, name="获取音频文件")
            # 模拟用户听音频和标注的时间
            time.sleep(random.uniform(0.5, 2))

        # 3. 准备标注数据
        emotions = ["<|HAPPY|>", "<|SAD|>", "<|ANGRY|>", "<|NEUTRAL|>", "<|FEARFUL|>"]
        events = ["<|Cry|>", "<|Sneeze|>", "<|Breath|>", "<|Cough|>", "<|Sing|>", "<|Speech|>"]
        languages = ["<|zh|>", "<|en|>"]

        # 模拟用户标注
        marked_data = {
            "key": f"mark_{json_data['source']}",
            "text_language": random.choice(languages),
            "emo_target": random.choice(emotions),
            "event_target": random.choice(events),
            "with_or_wo_itn": "<|withitn|>",
            "target": json_data.get("target"),
            "source": json_data.get("source", ""),
            "target_len": json_data.get("target_len"),
            "source_len": json_data.get("source_len")
        }

        # 4. 提交标注结果
        self.client.post(
            "/modify_file",
            json={"json_content": json.dumps(marked_data), "user_id": self.user_id},
            name="提交标注结果"
        )

    @task(1)
    def get_status(self):
        # 随机检查状态
        self.client.get("/get_total", name="获取剩余任务数")
        self.client.get("/get_completedFiles", name="获取已完成任务数")

# 运行命令: locust -f locust_test.py --host=http://localhost:6002 --users=100 --spawn-rate=10
# 其中 --users=100 表示总共模拟100个用户
# --spawn-rate=10 表示每秒增加10个用户