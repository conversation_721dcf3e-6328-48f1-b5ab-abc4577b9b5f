import json
import uuid
import random
import time
from locust import HttpUser, task, between, events
from collections import deque


# 创建全局计数器和锁，确保在多进程环境中正确计数
from locust.runners import STATE_STOPPING, STATE_STOPPED, STATE_CLEANUP, WorkerRunner
import threading

# 全局计数器和锁
task_counter = 0
counter_lock = threading.Lock()

# 全局错误计数
errors_count = 0
MAX_ERRORS = 50  # 最大允许的错误数量

@events.init.add_listener
def on_locust_init(environment, **kwargs):
    """初始化事件，设置全局变量"""
    global task_counter
    if not isinstance(environment.runner, WorkerRunner):
        # 分布式运行时，只在主进程中初始化
        environment.runner.task_counter = 0
        environment.runner.max_tasks = 1000

@events.request.add_listener
def on_request(request_type, name, response_time, response_length, exception, **kwargs):
    """跟踪获取任务的请求"""
    global task_counter, errors_count
    
    # 如果是错误请求，增加错误计数
    if exception is not None and name == "获取新任务（限制1000次）":
        with counter_lock:
            errors_count += 1
            print(f"错误数量增加到: {errors_count}/{MAX_ERRORS}")
            if errors_count >= MAX_ERRORS:
                print("错误数量超过阈值，建议终止测试")
                # 这里不直接终止，让用户决定是否手动停止
    
    # 只在成功获取任务时增加计数
    if name == "获取新任务（限制1000次）" and exception is None:
        with counter_lock:
            if hasattr(kwargs['response'], 'json'):
                try:
                    json_data = kwargs['response'].json()
                    if json_data and "json_data" in json_data and json_data["json_data"]:
                        task_counter += 1
                except:
                    pass


class SenseVoiceUser(HttpUser):
    """模拟用户访问SenseVoice数据标注系统的行为"""

    # 设置任务之间的等待时间，模拟真实用户思考时间 - 增加等待时间降低服务器压力
    wait_time = between(5, 10)  # 增加等待时间
    
    # 最大获取任务数
    max_tasks_to_fetch = 1000
    
    def on_start(self):
        """用户会话开始时执行的操作"""
        # 为每个模拟用户创建一个唯一的ID
        self.user_id = str(uuid.uuid4())
        # 为每个用户创建一个任务队列，存储待提交的任务
        self.pending_tasks = deque()
        # 用户是否已经停止获取新任务
        self.stop_fetching = False
        # 每个用户的错误尝试次数
        self.error_retries = 0
        self.max_retries = 3

    @task(1)
    def index_page(self):
        """任务一：访问主页"""
        self.client.get("/vvue", name="访问主页")

    @task(10)
    def process_task_workflow(self):
        """
        修改后的任务处理流程：
        1. 如果未达到1000次获取上限，则获取新任务并加入队列
        2. 如果队列中有任务，则提交一个任务
        3. 如果已达到获取上限且队列为空，则不执行操作
        """
        global task_counter, errors_count
        
        # 如果错误太多，停止新的请求
        if errors_count >= MAX_ERRORS:
            return
        
        # 检查是否需要获取新任务
        should_fetch = False
        with counter_lock:
            if task_counter < self.max_tasks_to_fetch and not self.stop_fetching:
                should_fetch = True
        
        if should_fetch:
            try:
                # 尝试获取新任务，添加错误处理
                with self.client.get(
                    f"/get_file?user_id={self.user_id}",
                    name="获取新任务（限制1000次）",
                    catch_response=True
                ) as response:
                    if response.status_code == 500:
                        # 记录500错误但不抛出异常，允许重试
                        response.failure(f"服务器返回500错误: {response.text}")
                        self.error_retries += 1
                        
                        # 超过最大重试次数则停止获取
                        if self.error_retries >= self.max_retries:
                            print(f"用户 {self.user_id} 达到最大重试次数，停止获取")
                            self.stop_fetching = True
                            
                        # 在重试前增加小的延迟，避免立即重试对服务器造成压力
                        time.sleep(2)  
                        return
                    elif response.status_code != 200:
                        response.failure(f"非200响应: {response.status_code}")
                        return
                    
                    # 成功获取响应，重置重试计数
                    self.error_retries = 0
                    
                    try:
                        # 解析JSON响应
                        json_data = response.json()
                        if "json_data" in json_data and json_data["json_data"]:
                            current_task = json_data.get("json_data")
                            # 将任务添加到队列
                            self.pending_tasks.append(current_task)
                            
                            # 再次检查计数，如果已经达到或超过上限，停止获取
                            with counter_lock:
                                if task_counter >= self.max_tasks_to_fetch:
                                    self.stop_fetching = True
                                    print(f"用户 {self.user_id} 已达到任务获取上限，停止获取新任务")
                    except Exception as e:
                        response.failure(f"解析响应失败: {str(e)}")
            except Exception as e:
                print(f"获取任务失败: {str(e)}")
        
        # 如果队列中有待处理的任务，则提交一个
        if self.pending_tasks:
            task_to_submit = self.pending_tasks.popleft()
            task_id = task_to_submit.get("id")
            
            # 随机决定标记为正常完成还是"OTHER"
            is_other = random.choice([True, False, False, False])  # 25%概率标记为OTHER
            emo_target = "<|OTHER|>" if is_other else "<|HAPPY|>"
            
            # 准备提交的数据
            modified_data = {
                "target": task_to_submit.get("target", "") or "这是一段测试文本",
                "emo_target": emo_target,
                "event_target": "测试事件",
                "text_language": "zh",
                "source": task_to_submit.get("source"),
                "source_len": task_to_submit.get("source_len"),
                "with_or_wo_itn": task_to_submit.get("with_or_wo_itn"),
                "semantic_emo_target": "测试语义情感" if not is_other else "<|OTHER|>"
            }
            
            # 提交任务
            try:
                with self.client.post(
                    "/modify_file",
                    json={
                        "user_id": self.user_id,
                        "task_id": task_id,
                        "json_content": modified_data
                    },
                    name="提交已获取的任务",
                    catch_response=True
                ) as response:
                    if response.status_code != 200:
                        response.failure(f"提交失败: {response.status_code}")
            except Exception as e:
                print(f"提交任务失败: {str(e)}")

    @task(2)
    def get_statistics(self):
        """任务三：随机获取统计信息"""
        # 如果错误太多，停止新的请求
        if errors_count >= MAX_ERRORS:
            return
            
        # 50%概率查询待处理任务，50%概率查询已完成任务
        if random.choice([True, False]):
            self.client.get("/get_total", name="获取待处理任务数量")
        else:
            self.client.get("/get_completedFiles", name="获取已完成任务数量")

    @task(1)
    def check_counter(self):
        """定期检查计数器状态"""
        global task_counter
        with counter_lock:
            if task_counter >= self.max_tasks_to_fetch and not self.stop_fetching:
                self.stop_fetching = True
                print(f"用户 {self.user_id} 检测到全局计数达到上限 {task_counter}/{self.max_tasks_to_fetch}，停止获取新任务")

# 以下是命令行指令，不在Python代码中执行
# 运行测试: locust -f locust_test.py --host=http://服务器IP:5002 --users 50 --spawn-rate 5
# --users 50: 降低为50个用户，避免服务器过载
# --spawn-rate 5: 每秒增加5个用户，降低启动压力
# 访问 http://localhost:8089 查看Web界面和测试结果