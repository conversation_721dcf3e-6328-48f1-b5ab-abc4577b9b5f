# 音频标注系统并发测试

本项目使用Locust进行音频标注系统的并发性能测试。

## 环境准备

1. 安装Locust
```bash
pip install locust
```

2. 确保音频标注服务正在运行
```bash
# 假设您的服务在本地运行
python Sensevoice_use/SenseVoice_train/dataserver_voice_mark.py
```

## 运行测试

1. 使用命令行启动Locust测试
```bash
locust -f locust_test.py --host=http://localhost:6002
```

2. 访问Web界面
打开浏览器访问 http://localhost:8089/ 配置测试参数：
   - Number of users: 100（模拟100个并发用户）
   - Spawn rate: 10（每秒新增10个用户）
   - Host: http://localhost:6002（如果使用其他主机，请更改）

3. 点击"Start swarming"开始测试

## 测试内容

该测试会模拟用户进行以下操作：
- 获取用户ID（首次访问）
- 获取待标注文件
- 下载音频文件
- 提交标注结果
- 查询系统进度

## 分析结果

测试完成后，Locust界面会显示以下数据：
- 请求响应时间统计
- 每秒请求数（RPS）
- 失败率
- 各端点的性能数据

可以通过图表查看系统在不同并发用户数下的性能表现。 