<!DOCTYPE html>
<html lang="zh" class="dark">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频标注</title>
    <!-- Import style -->
    <link rel="stylesheet" href="//unpkg.com/element-plus/dist/index.css" />
    <!-- Import Vue 3 -->
    <script src="//unpkg.com/vue@3"></script>
    <!-- Import component library -->
    <script src="//unpkg.com/element-plus"></script>
    <!-- WaveSurfer.js -->
    <script src="https://unpkg.com/wavesurfer.js@6.4.0/dist/wavesurfer.js"></script>
    <script src="https://unpkg.com/wavesurfer.js@6.4.0/dist/plugin/wavesurfer.regions.min.js"></script>
    <!-- 添加时间轴插件 -->
    <script src="https://unpkg.com/wavesurfer.js@6.4.0/dist/plugin/wavesurfer.timeline.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-plus/theme-chalk/dark/css-vars.css">
    <script>
                function getCookie(name) {
                const value = `; ${document.cookie}`;
                const parts = value.split(`; ${name}=`);
                if (parts.length === 2) return parts.pop().split(';').shift();
                }

                const userId = getCookie('user_id');
                console.log('Your user ID:', userId);
    </script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #121212;
            color: #e0e0e0;
        }

        h1 {
            text-align: center;
            color: #ffffff;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #1e1e1e;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
        }

        audio {
            width: 100%;
            margin-bottom: 20px;
        }

        .text-length {
            font-size: 14px;
            color: #b0b0b0;
        }

        .status {
            margin-bottom: 15px;
            font-weight: bold;
            text-align: center;
            color: #ffffff;
        }

        #waveform {
            width: 100%;
            height: 128px;
            margin-bottom: 0; /* 修改：移除底部边距，为时间轴留出空间 */
        }

        /* 添加时间轴样式 */
        #timeline {
            width: 100%;
            height: 30px;
            margin-bottom: 20px;
            color: #b0b0b0;
        }

        .crop-controls {
            margin-bottom: 20px;
        }
        
        /* 选区信息样式 */
        .region-info {
            font-size: 12px;
            margin-top: 5px;
            margin-bottom: 15px;
            color: #b0b0b0;
        }
    </style>
</head>

<body>
{% raw %}
    <div id="app" class="container">
        <p>用户ID: <span id="user-id"></span></p>
        <script>
          // 动态显示 user_id
          document.getElementById('user-id').textContent = userId || 'N/A';
        </script>
        <h1>音频标注工具</h1>
        <audio id="audioPlayer" controls autoplay></audio>

        <!-- Waveform and Cropping -->
        <div id="waveform"></div>
        <!-- 添加时间轴容器 -->
        <div id="timeline"></div>
        <!-- 添加选区信息显示 -->
        <div class="region-info" v-if="activeRegionDisplay">
            当前选区: {{ formatTime(activeRegionDisplay.start) }} - {{ formatTime(activeRegionDisplay.end) }} (时长: {{ formatDuration(activeRegionDisplay.end - activeRegionDisplay.start) }})
        </div>
        <div class="crop-controls">
            <el-button @click="playSelection" :disabled="!activeRegion">播放选区</el-button>
            <el-button @click="resetSelection" :disabled="allRegions.length === 0">重置选区</el-button>
            <el-button type="warning" @click="cropAudio" :disabled="allRegions.length === 0">确认裁剪</el-button>
        </div>

        <!-- 创建独立任务选项 -->
        <div class="create-task-options" style="margin-bottom: 20px;">
            <el-checkbox v-model="createIndependentTask">创建独立任务</el-checkbox>
            <template v-if="createIndependentTask">
                <el-input 
                    v-model="audioSuffix" 
                    placeholder="请输入新音频后缀" 
                    style="width: 200px; margin-left: 10px;"
                    @blur="checkAudioSuffix"
                    clearable>
                </el-input>
                <span v-if="suffixError" style="color: #ff4949; margin-left: 10px; font-size: 12px;">
                    {{ suffixErrorMsg }}
                </span>
            </template>
        </div>

        <!-- 查询已创建任务 -->
        <div class="query-tasks" style="margin-bottom: 20px;">
            <el-button type="info" @click="showTasksDialog">查询已创建任务</el-button>
            
            <!-- 任务查询对话框 -->
            <el-dialog
                v-model="tasksDialogVisible"
                title="已创建任务列表"
                width="80%"
                :close-on-click-modal="false"
            >
                <el-input 
                    v-model="taskSearchQuery" 
                    placeholder="输入后缀或路径查询"
                    style="margin-bottom: 15px;"
                    clearable
                    @input="filterTasks">
                </el-input>
                
                <el-table 
                    :data="filteredTasks" 
                    style="width: 100%" 
                    height="400" 
                    v-loading="isTasksLoading"
                    element-loading-text="加载中..."
                >
                    <el-table-column prop="id" label="ID" width="60"></el-table-column>
                    <el-table-column prop="source" label="音频路径" min-width="200"></el-table-column>
                    <el-table-column prop="target" label="文本内容" min-width="200"></el-table-column>
                    <el-table-column prop="text_language" label="语言" width="100"></el-table-column>
                    <el-table-column prop="emo_target" label="情感" width="120"></el-table-column>
                    <el-table-column prop="event_target" label="事件" width="120"></el-table-column>
                    <el-table-column prop="status" label="状态" width="100"></el-table-column>
                    <el-table-column label="操作" width="150">
                        <template #default="scope">
                            <el-button size="small" @click="loadTask(scope.row)">加载</el-button>
                        </template>
                    </el-table-column>
                    <template #empty>
                        <div style="text-align: center; padding: 20px;">
                            <p v-if="isTasksLoading">正在加载数据，请稍候...</p>
                            <p v-else>暂无数据</p>
                        </div>
                    </template>
                </el-table>
                
                <template #footer>
                    <div class="dialog-footer">
                        <el-button @click="tasksDialogVisible = false">关闭</el-button>
                    </div>
                </template>
            </el-dialog>
        </div>

        <div class="status">
            <span id="completedCount">已完成: 0</span>
            <span id="totalCount">剩余: 0</span>
        </div>

        <!-- 进度条 -->
        <div class="demo-progress">
            <el-progress :percentage="completionPercentage" />
            <el-progress :percentage="100" :format="format" />
            <el-progress :percentage="100" status="success" />
            <el-progress :percentage="100" status="warning" />
            <el-progress :percentage="50" status="exception" />
        </div>

        <!-- 语言选择 -->
        <label>语言选择: </label>
        <el-radio-group v-model="selectedLanguage">
            <el-radio-button label="<|zh|>" value="<|zh|>">中文</el-radio-button>
            <el-radio-button label="<|en|>" value="<|en|>">英文</el-radio-button>
        </el-radio-group>

        <br><br>

        <!-- 情感选择 -->
        <label>语调情感选择: </label>
        <el-radio-group v-model="selectedEmotion">
            <el-radio-button label="<|HAPPY|>" value="<|HAPPY|>">开心</el-radio-button>
            <el-radio-button label="<|SAD|>" value="<|SAD|>">伤心</el-radio-button>
            <el-radio-button label="<|ANGRY|>" value="<|ANGRY|>">生气</el-radio-button>
            <el-radio-button label="<|NEUTRAL|>" value="<|NEUTRAL|>">自然</el-radio-button>
            <el-radio-button label="<|FEARFUL|>" value="<|FEARFUL|>">害怕</el-radio-button>
            <el-radio-button label="<|OTHER|>" value="<|OTHER|>">其他</el-radio-button>
            <el-radio-button disabled label="<|DISGUSTED|>" value="<|DISGUSTED|>">厌恶</el-radio-button>
            <el-radio-button disabled label="<|SURPRISED|>" value="<|SURPRISED|>">惊讶</el-radio-button>
            
        </el-radio-group>

        <br><br>

        <!-- 事件选择 -->
        <label>事件选择: </label>
        <el-radio-group v-model="selectedEvent" size="large">
            <el-radio-button label="<|Cry|>" value="<|Cry|>">哭泣</el-radio-button>
            <el-radio-button label="<|Sneeze|>" value="<|Sneeze|>">打喷嚏</el-radio-button>
            <el-radio-button label="<|Breath|>" value="<|Breath|>">呼吸</el-radio-button>
            <el-radio-button label="<|Cough|>" value="<|Cough|>">咳嗽</el-radio-button>
            <el-radio-button label="<|Sing|>" value="<|Sing|>">歌唱</el-radio-button>
            <el-radio-button label="<|Speech|>" value="<|Speech|>">说话</el-radio-button>
            <el-radio-button label="<|BGM|>" value="<|BGM|>">背景音乐</el-radio-button>
            <el-radio-button label="<|Laughter|>" value="<|Laughter|>">笑声</el-radio-button>
            <el-radio-button label="<|Applause|>" value="<|Applause|>">掌声</el-radio-button>
            <el-radio-button label="<|Speech_Noise|>" value="<|Speech_Noise|>">说话噪音</el-radio-button>
        </el-radio-group>

        <br><br>

        <!-- 文本框 -->
        <label for="jsonEditor">文本内容: </label>
        <el-input type="textarea" id="jsonEditor" :rows="10" v-model="jsonContent" @input="updateTargetLength"></el-input>

        <br><br>

        <el-button v-if="isEditingTask" type="info" @click="returnToPreviousTask">返回上一任务</el-button>
        <el-button type="primary" @click="saveChanges" :disabled="noDataAvailable">保存修改</el-button>
        <el-button type="success" @click="saveAsNewTask" :disabled="noDataAvailable || !createIndependentTask || !audioSuffix">保存新增任务</el-button>
        <el-button type="danger" @click="clearContent" :disabled="noDataAvailable">清空文本框</el-button>
    </div>
{% endraw %}

    <script>
        const { createApp, ref, computed, onMounted } = Vue;
        const { ElInput, ElButton, ElRadioGroup, ElRadioButton, ElMessage, ElCheckbox, ElProgress, ElDialog, ElTable, ElTableColumn } = ElementPlus;
        const format = (percentage) => (percentage === 100 ? 'Full' : `${percentage}%`)

        createApp({
            components: {
                ElInput,
                ElButton,
                ElRadioGroup,
                ElRadioButton,
                ElCheckbox,
                ElProgress,
                ElDialog,
                ElTable,
                ElTableColumn
            },

            setup() {
                const jsonContent = ref('');
                const selectedLanguage = ref('<|zh|>');
                const selectedEmotion = ref('<|HAPPY|>');
                const selectedEvent = ref('<|Cry|>'); 
                const source_len = ref(0);
                const targetLength = ref(0);
                const source = ref('');
                const initialSource = ref(''); // 保存初始加载的音频路径
                const currentTaskId = ref(null); // 保存当前任务ID
                const wavesurfer = ref(null); // wavesurfer 实例
                const activeRegion = ref(null); // 当前选区
                const allRegions = ref([]); // 存储所有选区
                const activeRegionDisplay = ref(null); // 用于显示的选区信息
                const createIndependentTask = ref(false); // 是否创建独立任务
                const audioSuffix = ref(''); // 新音频后缀
                const suffixError = ref(false); // 后缀错误状态
                const suffixErrorMsg = ref(''); // 后缀错误信息
                
                // 已创建任务查询相关
                const tasksDialogVisible = ref(false); // 任务对话框显示状态
                const allCreatedTasks = ref([]); // 所有已创建的任务
                const filteredTasks = ref([]); // 过滤后的任务
                const taskSearchQuery = ref(''); // 任务搜索关键词
                const isEditingTask = ref(false); // 是否正在编辑已有任务
                const isTasksLoading = ref(false); // 是否正在加载任务列表
                const previousTaskState = ref(null); // 保存上一个任务的状态
                
                const totalFiles = ref(0);
                const completedFiles = ref(0);
                const noDataAvailable = ref(false);
                
                // 计算完成百分比
                const completionPercentage = computed(() => {
                    if (totalFiles.value + completedFiles.value === 0) return 0;
                    return Math.floor((completedFiles.value / (totalFiles.value + completedFiles.value)) * 100);
                });

                // 格式化时间为分:秒.毫秒
                const formatTime = (timeInSeconds) => {
                    const minutes = Math.floor(timeInSeconds / 60);
                    const seconds = Math.floor(timeInSeconds % 60);
                    const milliseconds = Math.floor((timeInSeconds % 1) * 1000);
                    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(3, '0')}`;
                };
                
                // 格式化时长
                const formatDuration = (durationInSeconds) => {
                    const seconds = Math.floor(durationInSeconds);
                    const milliseconds = Math.floor((durationInSeconds % 1) * 1000);
                    return `${seconds}秒${milliseconds}毫秒`;
                };

                const initWavesurfer = (audioUrl) => {
                    if (wavesurfer.value) {
                        wavesurfer.value.destroy();
                    }
                    
                    wavesurfer.value = WaveSurfer.create({
                        container: '#waveform',
                        waveColor: 'violet',
                        progressColor: 'purple',
                        height: 128,
                        plugins: [
                            WaveSurfer.regions.create({
                                regionsMinLength: 0.1,
                                regions: [],
                                dragSelection: {
                                    slop: 5
                                }
                            }),
                            // 添加时间轴插件
                            WaveSurfer.timeline.create({
                                container: '#timeline',
                                primaryFontColor: '#b0b0b0',
                                secondaryFontColor: '#8a8a8a',
                                primaryColor: '#b0b0b0',
                                secondaryColor: '#505050',
                                fontFamily: 'Arial',
                                fontSize: 12
                            })
                        ]
                    });

                    wavesurfer.value.load(audioUrl);

                    wavesurfer.value.on('ready', () => {
                         // 准备好后可以自动播放
                    });

                    // 修改：允许创建多个选区，并跟踪它们
                    wavesurfer.value.on('region-created', (region) => {
                        activeRegion.value = region;
                        // 给每个区域添加一个唯一ID
                        region.id = Date.now().toString();
                        // 添加到选区列表
                        allRegions.value.push(region);
                        activeRegionDisplay.value = { start: region.start, end: region.end };
                        
                        // 为每个区域添加点击事件，使其成为活动区域
                        region.element.addEventListener('click', (e) => {
                            e.stopPropagation(); // 阻止事件冒泡
                            // 设置为活动区域
                            activeRegion.value = region;
                            activeRegionDisplay.value = { start: region.start, end: region.end };
                            // 可视化标记当前活动区域（可选）
                            highlightActiveRegion();
                        });
                    });
                    
                    wavesurfer.value.on('region-updated', (region) => {
                        activeRegion.value = region;
                        activeRegionDisplay.value = { start: region.start, end: region.end };
                        // 更新时也更新高亮
                        highlightActiveRegion();
                    });

                    wavesurfer.value.on('region-removed', (region) => {
                        // 从列表中删除该区域
                        allRegions.value = allRegions.value.filter(r => r.id !== region.id);
                        // 如果被删除的是当前活动区域，则设置为null
                        if (activeRegion.value && activeRegion.value.id === region.id) {
                            activeRegion.value = null;
                            activeRegionDisplay.value = null;
                        }
                    });
                    
                    // 点击波形空白处取消选择
                    wavesurfer.value.container.addEventListener('click', (e) => {
                        // 确保点击的是波形容器本身，而不是区域
                        if (e.target === wavesurfer.value.container || e.target.tagName === 'WAVE') {
                            activeRegion.value = null;
                            activeRegionDisplay.value = null;
                            // 重置所有区域的样式
                            resetRegionStyles();
                        }
                    });
                };
                
                // 高亮当前活动区域
                const highlightActiveRegion = () => {
                    if (!wavesurfer.value) return;
                    
                    // 首先重置所有区域的样式
                    resetRegionStyles();
                    
                    // 然后高亮活动区域
                    if (activeRegion.value) {
                        const activeElement = activeRegion.value.element;
                        if (activeElement) {
                            activeElement.style.backgroundColor = 'rgba(255, 100, 100, 0.3)'; // 更明显的红色
                            activeElement.style.border = '2px solid red';
                        }
                    }
                };
                
                // 重置所有区域的样式
                const resetRegionStyles = () => {
                    if (!wavesurfer.value) return;
                    
                    Object.values(wavesurfer.value.regions.list).forEach(region => {
                        if (region.element) {
                            region.element.style.backgroundColor = 'rgba(0, 0, 0, 0.1)'; // 默认颜色
                            region.element.style.border = 'none';
                        }
                    });
                };

                const fetchFile = async () => {
                    // 重置编辑状态
                    isEditingTask.value = false;
                    previousTaskState.value = null;
                    console.log("获取新文件, isEditingTask:", isEditingTask.value);

                    const response = await fetch(`/get_file?user_id=${userId}`);
                    const data = await response.json();
                    if (data.json_data) {
                        console.log(data.json_data);
                        const jsonData = data.json_data;
                        // 保存任务ID
                        currentTaskId.value = jsonData.id;
                        
                        selectedLanguage.value = jsonData.text_language || '<|zh|>';
                        selectedEmotion.value = jsonData.emo_target || '<|HAPPY|>';
                        selectedEvent.value = jsonData.event_target || '<|Cry|>';
                        
                        source.value = jsonData.source;
                        initialSource.value = jsonData.source; // 记录初始路径
                        source_len.value = jsonData.source_len;
                        jsonContent.value = jsonData.target || '';
                        targetLength.value = jsonContent.value.replace(/\s+/g, '').length;
                        
                        const audioFileName = jsonData.source;
                        const audioUrl = `/audio/${audioFileName}`;
                        document.getElementById('audioPlayer').src = audioUrl;
                        document.getElementById('audioPlayer').play();

                        // 初始化 wavesurfer
                        initWavesurfer(audioUrl);
                        
                        // 清空选区信息，确保不显示上一个任务的选区
                        activeRegion.value = null;
                        allRegions.value = [];
                        activeRegionDisplay.value = null;
                        
                        // 有数据可用
                        noDataAvailable.value = false;
                    } else {
                        // 没有更多数据可处理
                        ElMessage({
                            message: "所有数据已处理完毕，没有更多数据需要标注。",
                            type: 'success',
                            duration: 5000
                        });
                        
                        // 清空表单并标记为无数据
                        jsonContent.value = '';
                        source.value = '';
                        document.getElementById('audioPlayer').src = '';
                        if (wavesurfer.value) wavesurfer.value.empty();
                        currentTaskId.value = null;
                        noDataAvailable.value = true;
                    }
                    updateStatus();
                };

                const updateTargetLength = () => {
                    const cleanedText = jsonContent.value.replace(/\s+/g, '');
                    targetLength.value = cleanedText.length;
                };

                const cropAudio = async () => {
                    if (allRegions.value.length === 0) {
                        ElMessage({ message: "请至少选择一个区域进行裁剪", type: 'warning' });
                        return;
                    }
                    
                    const regions = allRegions.value.map(region => ({
                        start_time: region.start,
                        end_time: region.end
                    }));

                    try {
                        const response = await fetch('/crop_audio', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                source: initialSource.value,
                                regions: regions,
                                task_id: currentTaskId.value,
                                is_new_task: createIndependentTask.value // 传递标志
                            })
                        });

                        const data = await response.json();
                        if (data.code === 200) {
                            ElMessage({ message: "音频裁剪成功!", type: 'success' });
                            // 更新当前视图的音频路径和长度
                            source.value = data.new_source;
                            source_len.value = data.new_source_len;
                        } else {
                            ElMessage({ message: `裁剪失败: ${data.message}`, type: 'error' });
                        }
                    } catch (error) {
                        console.error("裁剪请求失败:", error);
                        ElMessage({ message: "裁剪请求失败", type: 'error' });
                    }
                };
                
                const playSelection = () => {
                    if (activeRegion.value) {
                        activeRegion.value.play();
                    }
                };

                const resetSelection = () => {
                    // 删除所有选区
                    if (wavesurfer.value) {
                        Object.keys(wavesurfer.value.regions.list).forEach(id => {
                            wavesurfer.value.regions.list[id].remove();
                        });
                    }
                    allRegions.value = [];
                    activeRegion.value = null;
                    activeRegionDisplay.value = null;
                };

                const saveChanges = async () => {
                    // 检查必要数据
                    if (!currentTaskId.value) {
                        ElMessage({
                            message: "无法提交，任务ID不存在",
                            type: 'error'
                        });
                        return;
                    }

                    const fileName = document.getElementById('audioPlayer').src.split('/').pop();
                    const jsonData = {
                        key: `mark_${fileName}`,
                        text_language: selectedLanguage.value,
                        emo_target: selectedEmotion.value,
                        event_target: selectedEvent.value,
                        with_or_wo_itn: "<|withitn|>",
                        target: jsonContent.value,
                        source: source.value,
                        target_len: jsonContent.value.replace(/\s+/g, '').length,
                        source_len: source_len.value
                    };

                    try {
                        // 根据是否是编辑模式选择不同的接口
                        const endpoint = isEditingTask.value ? '/update_task' : '/modify_file';
                        
                        const response_m = await fetch(endpoint, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                json_content: jsonData,
                                user_id: userId,
                                task_id: currentTaskId.value
                            }),
                        });
                        
                        const data_m = await response_m.json();
                        if (data_m.code !== 200) {
                            ElMessage({
                                message: data_m.message,
                                type: 'warning',
                            });
                        } else {
                            ElMessage({
                                message: isEditingTask.value ? "任务更新成功" : "修改成功",
                                type: 'success',
                            });
                            
                            if (isEditingTask.value) {
                                // 如果是编辑模式，不获取下一个任务
                                // 重置编辑状态
                                isEditingTask.value = false;
                                console.log("退出编辑模式, isEditingTask:", isEditingTask.value);
                                // 更新任务列表
                                if (tasksDialogVisible.value) {
                                    await fetchCreatedTasks();
                                }
                            } else {
                                // 如果不是编辑模式，则获取下一个任务
                                totalFiles.value--;
                                completedFiles.value = await fetchcompletedFiles();
                                updateStatus();
                                await fetchFile();
                            }
                        }
                    } catch (error) {
                        console.error("提交数据时发生错误:", error);
                        ElMessage({
                            message: "提交失败，请重试",
                            type: 'error'
                        });
                    }
                };

                const clearContent = () => {
                    jsonContent.value = ''; 
                    updateTargetLength();
                };

                const saveAsNewTask = async () => {
                    // 检查必要数据
                    if (!currentTaskId.value) {
                        ElMessage({
                            message: "无法提交，任务ID不存在",
                            type: 'error'
                        });
                        return;
                    }
                    
                    if (!createIndependentTask.value || !audioSuffix.value) {
                        ElMessage({
                            message: "请勾选创建独立任务并填写音频后缀",
                            type: 'warning'
                        });
                        return;
                    }

                    // 检查后缀是否有效
                    if (!audioSuffix.value.match(/^[a-zA-Z0-9_]+$/)) {
                        suffixError.value = true;
                        suffixErrorMsg.value = '请输入有效的字母、数字和下划线';
                        ElMessage({
                            message: "请输入有效的音频后缀",
                            type: 'warning'
                        });
                        return;
                    }

                    const fileName = document.getElementById('audioPlayer').src.split('/').pop();
                    const jsonData = {
                        key: `mark_${fileName}`,
                        text_language: selectedLanguage.value,
                        emo_target: selectedEmotion.value,
                        event_target: selectedEvent.value,
                        with_or_wo_itn: "<|withitn|>",
                        target: jsonContent.value,
                        source: source.value,
                        target_len: jsonContent.value.replace(/\s+/g, '').length,
                        source_len: source_len.value
                    };

                    try {
                        const response = await fetch('/save_new_task', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                json_content: jsonData,
                                user_id: userId,
                                task_id: currentTaskId.value,
                                audio_suffix: audioSuffix.value
                            }),
                        });
                        
                        const data = await response.json();
                        if (data.code !== 200) {
                            // 检查是否是唯一约束错误
                            if (data.message && data.message.includes('UNIQUE constraint failed')) {
                                ElMessage({
                                    message: `后缀"${audioSuffix.value}"已被使用，请使用不同的后缀名`,
                                    type: 'error',
                                    duration: 5000
                                });
                                suffixError.value = true;
                                suffixErrorMsg.value = '此后缀已被使用';
                            } else {
                                ElMessage({
                                    message: data.message,
                                    type: 'warning',
                                });
                            }
                        } else {
                            ElMessage({
                                message: "新增任务保存成功",
                                type: 'success',
                            });
                            
                            // 更新任务数量统计，但不减少待标记数量
                            completedFiles.value = await fetchcompletedFiles(); // 更新已完成任务数
                            updateStatus();
                            
                            // 重置选项，但不刷新页面
                            createIndependentTask.value = false;
                            audioSuffix.value = '';
                            suffixError.value = false;
                            suffixErrorMsg.value = '';
                        }
                    } catch (error) {
                        console.error("提交新增任务数据时发生错误:", error);
                        ElMessage({
                            message: "新增任务提交失败，请重试",
                            type: 'error'
                        });
                    }
                };

                const updateStatus = () => {
                    document.getElementById('completedCount').textContent = `成功标记: ${completedFiles.value}`;
                    document.getElementById('totalCount').textContent = `剩余待标记: ${totalFiles.value}`;
                };

                onMounted(async () => {
                    try {
                        totalFiles.value = await fetchTotalFiles();
                        completedFiles.value = await fetchcompletedFiles();
                        await fetchFile();
                        updateStatus();
                    } catch (error) {
                        console.error("初始化时发生错误:", error);
                        ElMessage({
                            message: "加载数据失败，请刷新页面",
                            type: 'error'
                        });
                    }
                });

                const fetchTotalFiles = async () => {
                    const response = await fetch('/get_total');
                    const data = await response.json();
                    return data.total_len;
                };

                const fetchcompletedFiles = async () => {
                    const response = await fetch('/get_completedFiles');
                    const data = await response.json();
                    return data.total_len;
                };

                // 查询任务相关函数
                const showTasksDialog = async () => {
                    tasksDialogVisible.value = true;
                    // 清空上次的查询结果
                    allCreatedTasks.value = [];
                    filteredTasks.value = [];
                    taskSearchQuery.value = '';
                    // 重新获取任务列表
                    await fetchCreatedTasks();
                };

                const fetchCreatedTasks = async () => {
                    try {
                        isTasksLoading.value = true;
                        console.log("开始获取已创建任务...");
                        const response = await fetch('/get_created_tasks');
                        const data = await response.json();
                        console.log("已创建任务API返回:", data);
                        
                        if (data.tasks) {
                            allCreatedTasks.value = data.tasks;
                            filterTasks();
                            console.log("已加载任务列表:", allCreatedTasks.value.length, "条记录");
                        }
                    } catch (error) {
                        console.error("获取已创建任务失败:", error);
                        ElMessage({
                            message: "获取已创建任务失败",
                            type: 'error'
                        });
                    } finally {
                        isTasksLoading.value = false;
                    }
                };

                const filterTasks = () => {
                    if (!taskSearchQuery.value) {
                        filteredTasks.value = [...allCreatedTasks.value];
                        return;
                    }
                    
                    const query = taskSearchQuery.value.toLowerCase();
                    filteredTasks.value = allCreatedTasks.value.filter(task => 
                        task.source && task.source.toLowerCase().includes(query) ||
                        task.target && task.target.toLowerCase().includes(query) ||
                        task.id && task.id.toString().includes(query)
                    );
                };

                const loadTask = async (task) => {
                    try {
                        // 保存当前任务状态
                        if (!isEditingTask.value) {
                            previousTaskState.value = {
                                currentTaskId: currentTaskId.value,
                                selectedLanguage: selectedLanguage.value,
                                selectedEmotion: selectedEmotion.value,
                                selectedEvent: selectedEvent.value,
                                source: source.value,
                                initialSource: initialSource.value,
                                source_len: source_len.value,
                                jsonContent: jsonContent.value,
                                targetLength: targetLength.value
                            };
                        }

                        // 获取任务详细信息
                        const response = await fetch(`/get_task_detail?task_id=${task.id}`);
                        const data = await response.json();
                        
                        if (!data.task) {
                            ElMessage({
                                message: "加载任务失败，任务不存在",
                                type: 'error'
                            });
                            return;
                        }
                        
                        // 加载任务详情到界面
                        const taskDetail = data.task;
                        
                        currentTaskId.value = taskDetail.id;
                        selectedLanguage.value = taskDetail.text_language || '<|zh|>';
                        selectedEmotion.value = taskDetail.emo_target || '<|HAPPY|>';
                        selectedEvent.value = taskDetail.event_target || '<|Cry|>';
                        
                        source.value = taskDetail.source;
                        initialSource.value = taskDetail.source; // 记录初始路径
                        source_len.value = taskDetail.source_len;
                        jsonContent.value = taskDetail.target || '';
                        targetLength.value = jsonContent.value.replace(/\s+/g, '').length;
                        
                        // 加载音频
                        const audioFileName = taskDetail.source;
                        const audioUrl = `/audio/${audioFileName}`;
                        document.getElementById('audioPlayer').src = audioUrl;
                        document.getElementById('audioPlayer').play();

                        // 初始化 wavesurfer
                        initWavesurfer(audioUrl);
                        
                        // 清空选区信息
                        activeRegion.value = null;
                        allRegions.value = [];
                        activeRegionDisplay.value = null;
                        
                        // 标记为编辑模式
                        isEditingTask.value = true;
                        console.log("进入编辑模式, isEditingTask:", isEditingTask.value);
                        
                        // 关闭对话框
                        tasksDialogVisible.value = false;
                        
                        ElMessage({
                            message: "已加载任务，您现在可以编辑它",
                            type: 'success'
                        });
                    } catch (error) {
                        console.error("加载任务详情失败:", error);
                        ElMessage({
                            message: "加载任务详情失败",
                            type: 'error'
                        });
                    }
                };

                const returnToPreviousTask = () => {
                    if (!previousTaskState.value) {
                        ElMessage({
                            message: "没有可以返回的上一任务",
                            type: 'warning'
                        });
                        return;
                    }
                    
                    const prevState = previousTaskState.value;
                    
                    currentTaskId.value = prevState.currentTaskId;
                    selectedLanguage.value = prevState.selectedLanguage;
                    selectedEmotion.value = prevState.selectedEmotion;
                    selectedEvent.value = prevState.selectedEvent;
                    source.value = prevState.source;
                    initialSource.value = prevState.initialSource;
                    source_len.value = prevState.source_len;
                    jsonContent.value = prevState.jsonContent;
                    targetLength.value = prevState.targetLength;
                    
                    // 恢复音频
                    const audioUrl = `/audio/${prevState.source}`;
                    document.getElementById('audioPlayer').src = audioUrl;
                    initWavesurfer(audioUrl);
                    
                    // 重置状态
                    isEditingTask.value = false;
                    previousTaskState.value = null;
                    console.log("返回上一任务, isEditingTask:", isEditingTask.value);
                    
                    ElMessage({
                        message: "已返回到上一任务",
                        type: 'info'
                    });
                };

                const checkAudioSuffix = () => {
                    if (!audioSuffix.value) {
                        suffixError.value = false;
                        suffixErrorMsg.value = '';
                        return;
                    }
                    
                    if (!audioSuffix.value.match(/^[a-zA-Z0-9_]+$/)) {
                        suffixError.value = true;
                        suffixErrorMsg.value = '请输入有效的字母、数字和下划线';
                    } else {
                        suffixError.value = false;
                        suffixErrorMsg.value = '';
                    }
                };

                return {
                    jsonContent,
                    selectedLanguage,
                    selectedEmotion,
                    selectedEvent,
                    saveChanges,
                    updateTargetLength,
                    targetLength,
                    clearContent,
                    completionPercentage,
                    noDataAvailable,
                    format,
                    wavesurfer,
                    activeRegion,
                    allRegions,
                    activeRegionDisplay,
                    playSelection,
                    resetSelection,
                    cropAudio,
                    formatTime,
                    formatDuration,
                    createIndependentTask,
                    audioSuffix,
                    saveAsNewTask,
                    suffixError,
                    suffixErrorMsg,
                    checkAudioSuffix,
                    showTasksDialog,
                    fetchCreatedTasks,
                    filterTasks,
                    loadTask,
                    tasksDialogVisible,
                    allCreatedTasks,
                    filteredTasks,
                    taskSearchQuery,
                    isEditingTask,
                    isTasksLoading,
                    previousTaskState,
                    returnToPreviousTask
                };
            },
        }).mount('#app');
    </script>
</body>

</html> 