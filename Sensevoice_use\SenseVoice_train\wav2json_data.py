# pip3 install -U funasr funasr-onnx  # 这行是安装必要的语音识别库的命令
import json  # 导入json库，用于处理JSON格式的数据
from pathlib import Path  # 导入Path库，用于处理文件路径
import re  # 导入正则表达式库，用于文本匹配和处理
from funasr_onnx import SenseVoiceSmall  # 导入语音识别模型
from funasr import AutoModel  # 导入自动模型加载功能
from funasr_onnx.utils.postprocess_utils import rich_transcription_postprocess  # 导入后处理工具
import librosa  # 导入音频处理库，用于处理音频文件

#所采用的SenseVoice模型路径（这个模型可以识别语音中的文字和情感）
model_dir = "/home/<USER>/emotion2vec/SenseVoice/outputs_241213"

from FSNM import SegmentProcess  # 导入自定义的音频分段处理模块
import os  # 导入操作系统功能库，用于文件和目录操作

# 指定转换目录

class Wav2Json():
    """
    这个类的作用是将音频文件转换为JSON格式的数据
    这些数据包含了音频的文字内容、情感标签等信息
    """
    def __init__(self,vedio_dir,mp3_dir,output_json_path):
        """
        初始化函数，设置各种路径和加载模型
        
        参数:
        vedio_dir - 视频文件夹路径
        mp3_dir - MP3音频文件夹路径
        output_json_path - 输出JSON文件的路径
        """
        self.vedio_dir = vedio_dir  # 视频文件夹
        self.mp3_dir = mp3_dir  # 输入的mp3文件夹

        self.output_json_path = output_json_path  # 输出的json文件路径
        #self.model_dir = "iic/SenseVoiceSmall"
        self.model_dir = model_dir  # 使用前面定义的模型路径
        # 加载语音识别模型，设置使用GPU加速
        self.model = AutoModel(model=self.model_dir, trust_remote_code=True, device="cuda:0")

    def ensure_directory_exists(self, file_path):
        """
        确保文件路径的目录存在，如果不存在则创建
        
        参数:
        file_path - 文件的完整路径
        """
        directory = os.path.dirname(file_path)
        if directory and not os.path.exists(directory):
            os.makedirs(directory)
            print(f"已创建目录: {directory}")
         
    def list_files(self,directory):
        """
        列出指定目录中的所有文件
        
        参数:
        directory - 要列出文件的目录路径
        
        返回:
        文件名列表
        """
        return [
            f for f in os.listdir(directory)  # 遍历目录中的所有项目
            if os.path.isfile(os.path.join(directory, f))  # 只保留文件（不包括文件夹）
        ]
    
    def get_source_length(self,file_path):
        """
        计算音频文件的特征长度
        
        参数:
        file_path - 音频文件路径
        
        返回:
        音频特征的帧数
        """
        
        y, sr = librosa.load(file_path, sr=None)  # 加载音频文件，保持原始采样率

        # 设置 fbank 特征参数（fbank是一种音频特征提取方法）
        n_fft = 2048  # FFT窗口大小（快速傅里叶变换的窗口大小）
        hop_length = 512  # 每帧之间的间隔（两个相邻窗口之间的距离）
        n_mels = 128  # mel频率的数量（mel是一种特殊的频率单位，更接近人耳感知）

        # 计算 fbank 特征（将音频转换为频谱图）
        fbank = librosa.feature.melspectrogram(y=y, sr=sr, n_fft=n_fft, hop_length=hop_length, n_mels=n_mels)

        # 获取 fbank 帧数（特征的时间长度）
        source_len = fbank.shape[1]
        return source_len

    def extract_tags(self,text):
        """
        从文本中提取标签（格式为<|标签|>）
        
        参数:
        text - 包含标签的文本
        
        返回:
        提取出的四个标签：语言、情感、事件、是否使用ITN
        """
        # 匹配所有<|...|>格式的标签
        tags = re.findall(r'<\|([^|]+?)\|>', text)
        # 确保顺序正确：text_language, emo_target, event_target, with_or_wo_itn
        if len(tags) >= 4:
            return tags[0], tags[1], tags[2], tags[3]
        else:
            raise ValueError("text 中的标签数量不足或格式不正确")
            
    def check_and_add_markers(self,data):
        """
        检查并添加标记符号（<|和|>）到数据中的各个字段
        
        参数:
        data - 包含各种标签的数据字典
        
        返回:
        添加了标记符号的数据字典
        """
        
        # 如果语言标签没有正确的标记符号，添加它们
        if not data["text_language"].startswith("<|") or not data["text_language"].endswith("|>"):
            data["text_language"] = f"<|{data['text_language']}|>"
        
        # 如果情感标签没有正确的标记符号，添加它们
        if not data["emo_target"].startswith("<|") or not data["emo_target"].endswith("|>"):
            data["emo_target"] = f"<|{data['emo_target']}|>"
        
        # 如果事件标签没有正确的标记符号，添加它们
        if not data["event_target"].startswith("<|") or not data["event_target"].endswith("|>"):
            data["event_target"] = f"<|{data['event_target']}|>"
        
        # 如果ITN标签没有正确的标记符号，添加它们
        if not data["with_or_wo_itn"].startswith("<|") or not data["with_or_wo_itn"].endswith("|>"):
            data["with_or_wo_itn"] = f"<|{data['with_or_wo_itn']}|>"
    
        return data


    def process(self):
        """
        处理所有MP3文件，将它们转换为JSON格式
        这是主要的处理函数，包含了整个转换流程
        """
        try:
            # 确保输出目录存在
            self.ensure_directory_exists(self.output_json_path)
            
            file_names = self.list_files(self.mp3_dir)  # 获取所有MP3文件名
            new_data_list = []  # 存储处理后的数据
            generated_data = []  # 存储模型生成的原始数据
            
            # 遍历所有MP3文件进行处理
            for file_name in file_names:
                # 构建完整的MP3文件路径
                mp3_file = f"{self.mp3_dir}/{file_name}"
                try:
                    # 使用语音识别模型处理音频文件
                    res = self.model.generate(
                        input=f"{mp3_file}",
                        cache={},
                        language="zh",  # 设置语言为中文
                        use_itn=True,  # 使用ITN（逆文本规范化）
                        batch_size=64,  # 批处理大小
                        ban_emo_unk=True,  # 禁止未知情感标签
                    )
                    res[0]["filename"] = file_name  # 添加文件名到结果中
                    generated_data.append(res)  # 保存结果
                except Exception as e:
                    print(f"Error processing file {mp3_file}: {e}")  # 打印错误信息
                    
            # 处理所有生成的数据
            for item in generated_data:
                # 提取关键信息
                key = item[0]['key']  # 提取唯一标识符
                text = item[0]['text']  # 提取识别出的文本
                file_name = item[0]['filename']  # 提取文件名
                absolute_path = f"{self.mp3_dir}/{file_name}"  # 构建完整路径
                
                # 提取target字段（纯文本内容）
                target = re.sub(r'<\|.*?\|>', '', text)  # 去掉<|...|>的内容
                target1 = target.replace("，", "").replace("。", "")  # 去掉标点符号

                # 计算target_len（文本长度）
                target_len = len(target1)

                # 提取text_language、emo_target、event_target、with_or_wo_itn（各种标签）
                text_language, emo_target, event_target, with_or_wo_itn = self.extract_tags(text)
                
                # 获取source_len（音频特征长度）
                source_len = self.get_source_length(absolute_path)

                # 构建新的数据字典
                new_data = {
                    "key": key,
                    "text_language": text_language,  # 语言标签
                    "emo_target": emo_target,  # 情感标签
                    "event_target": event_target,  # 事件标签
                    "with_or_wo_itn": with_or_wo_itn,  # ITN标签
                    "target": target,  # 纯文本内容
                    "source": absolute_path,  # 音频文件路径
                    "target_len": target_len,  # 文本长度
                    "source_len": source_len  # 音频特征长度
                }
                new_data = self.check_and_add_markers(new_data)  # 检查并添加标记符号
                # 添加到新的数据列表
                new_data_list.append(new_data)

            # 将处理后的数据写入JSON文件
            with open(self.output_json_path, 'w', encoding='utf-8') as f:
                for record in new_data_list:
                    f.write(json.dumps(record, ensure_ascii=False) + '\n')  # 每行一条JSON记录

            print(f"数据已成功保存到 {self.output_json_path}")  # 打印成功信息
        except Exception as e:
            print(f"An error occurred: {e}")  # 打印错误信息

if __name__ == "__main__":
    """
    当直接运行这个脚本时执行的代码
    """
    
    video_dir = "/home/<USER>/下载/video_1213_perfinetrain"  # 输入视频文件夹
    mp3_dir = "/home/<USER>/emotion2vec/data/mp3/session1213"  # 输出的mp3保存文件夹
    temp_dir = "/home/<USER>/emotion2vec/data/mp3/temp"  # 临时中转文件夹
    output_json_path = "/home/<USER>/emotion2vec/data/json/session1213/session.jsonl"  # 转换后的待标记数据集文件路径

    # 创建切分类
    SP = SegmentProcess(video_dir, mp3_dir, temp_dir, True)  # 初始化音频分段处理器
    # 将目标mp4格式转换为mp3
    flag = SP.segprocess_mp4_to_mp3()  # 执行转换，flag为是否成功转换
    
    if flag:  # 如果转换成功
        # 如果flag为true，则进行wav2json
        # wav2json将会把mp3文件转化为json格式，并保存到output_json_path中
        wav2json = Wav2Json(video_dir, mp3_dir, output_json_path)  # 创建Wav2Json实例
        wav2json.process()  # 执行处理
    else:
        print("error")  # 打印错误信息
    
    