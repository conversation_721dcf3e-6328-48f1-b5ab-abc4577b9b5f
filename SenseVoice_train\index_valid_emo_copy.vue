<!DOCTYPE html>
<html lang="zh" class="dark">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频标注</title>
    <!-- Import style -->
    <link rel="stylesheet" href="//unpkg.com/element-plus/dist/index.css" />
    <!-- Import Vue 3 -->
    <script src="//unpkg.com/vue@3"></script>
    <!-- Import component library -->
    <script src="//unpkg.com/element-plus"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-plus/theme-chalk/dark/css-vars.css">

    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #121212;
            color: #e0e0e0;
        }

        h1 {
            text-align: center;
            color: #ffffff;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #1e1e1e;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
        }

        audio {
            width: 100%;
            margin-bottom: 20px;
        }

        .text-length {
            font-size: 14px;
            color: #b0b0b0;
        }

        .status {
            margin-bottom: 15px;
            font-weight: bold;
            text-align: center;
            color: #ffffff;
        }
    </style>
</head>

<body>
    <script>
    // 动态显示 user_id
    document.getElementById('user-id').textContent = userId || 'N/A';
    </script>
    <div id="app" class="container">
        <h1>音频标注工具</h1>
        <audio id="audioPlayer" controls autoplay></audio>

        <div class="status">
            <span id="completedCount">已完成: 0</span>
            <span id="totalCount">剩余: 0</span>
        </div>

        <!-- 进度条 -->
        <div class="demo-progress">
            <el-progress :percentage="50" />
            <el-progress :percentage="100" :format="format" />
            <el-progress :percentage="100" status="success" />
            <el-progress :percentage="100" status="warning" />
            <el-progress :percentage="50" status="exception" />
        </div>

 

        <!-- 语义情感选择（新增部分）-->
        <label>情感选择: </label>
        <el-radio-group v-model="selectedSemanticEmotion">
            <el-radio-button label="<|HAPPY|>" value="<|HAPPY|>">快乐</el-radio-button>
            <el-radio-button label="<|SAD|>" value="<|SAD|>">悲伤</el-radio-button>
            <el-radio-button label="<|ANGRY|>" value="<|ANGRY|>">愤怒</el-radio-button>
            <el-radio-button label="<|NEUTRAL|>" value="<|NEUTRAL|>">中立</el-radio-button>
            <el-radio-button label="<|FEARFUL|>" value="<|FEARFUL|>">害怕</el-radio-button>
            <el-radio-button label="<|DISGUSTED|>" value="<|DISGUSTED|>">厌恶</el-radio-button>
            <el-radio-button label="<|SURPRISED|>" value="<|SURPRISED|>">惊讶</el-radio-button>
            <el-radio-button label="<|OTHER|>" value="<|OTHER|>">其他</el-radio-button>
        </el-radio-group>

        <br><br>

     

        <!-- 文本框 -->
        <label for="jsonEditor">文本内容: </label>
        <el-input type="textarea" id="jsonEditor" :rows="10" v-model="jsonContent" @input="updateTargetLength"></el-input>

        <br><br>

        <el-button type="primary" @click="saveChanges">保存修改</el-button>
        <el-button type="danger" @click="clearContent">清空文本框</el-button>
    </div>

    <script>
        const { createApp, ref, onMounted } = Vue;
        const { ElInput, ElButton, ElRadioGroup, ElRadioButton, ElMessage } = ElementPlus;
        const format = (percentage) => (percentage === 100 ? 'Full' : `${percentage}%`)

        createApp({
            components: {
                ElInput,
                ElButton,
                ElRadioGroup,
                ElRadioButton
            },

            setup() {
                const jsonContent = ref('');
                const selectedLanguage = ref('<|zh|>');
                const selectedEmotion = ref('<|HAPPY|>');
                const selectedEvent = ref('<|Cry|>'); 
                const selectedSemanticEmotion = ref('开心'); // 新增语义情感
                const source_len = ref(0);
                const targetLength = ref(0);
                const source = ref('');

                let totalFiles = 0;
                let completedFiles = 0;
                // 从 Cookie 中读取 user_id
                function getCookie(name) {
                const value = `; ${document.cookie}`;
                const parts = value.split(`; ${name}=`);
                if (parts.length === 2) return parts.pop().split(';').shift();
                }

                const userId = getCookie('user_id');
                console.log('Your user ID:', userId);
                const fetchFile = async () => {
                    const response = await fetch('/get_file?user_id=${userId}');
                    const data = await response.json();
                    if (data.json_data) {
                        const jsonData = data.json_data;
                        selectedLanguage.value = jsonData.text_language;
                        selectedEmotion.value = jsonData.emo_target;
                        selectedEvent.value = jsonData.event_target;
                        // 获取语义情感数据
                        source.value = jsonData.source;
                        source_len.value = jsonData.source_len;
                        jsonContent.value = jsonData.target;
                        targetLength.value = jsonContent.value.replace(/[，。！？]/g, '').length;
                        const audioFileName = jsonData.source
                        document.getElementById('audioPlayer').src = `/audio/${audioFileName}`;
                        document.getElementById('audioPlayer').play();
                    } else {
                        alert(data.message);
                    }
                    updateStatus();
                };

                const updateTargetLength = () => {
                    cleanedText = jsonContent.value.replace(/[，。！？]/g, '');
                    targetLength.value = cleanedText.length;
                };

                const saveChanges = async () => {
                    const fileName = document.getElementById('audioPlayer').src.split('/').pop()
                    const jsonData = JSON.stringify({
                        semantic_emo_target: selectedSemanticEmotion.value, // 保存语义情感
                        target: jsonContent.value,
                        source: source.value,
                    });

                    await fetch('/modify_file', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ json_content: jsonData }),
                    });

                    ElMessage({
                        message: '修改已保存！',
                        type: 'success',
                    });

                    fetchFile();
                    totalFiles--;
                    completedFiles = await fetchcompletedFiles();
                    updateStatus();
                };

                const clearContent = () => {
                    jsonContent.value = ''; 
                };

                const updateStatus = () => {
                    document.getElementById('completedCount').textContent = `成功标记: ${completedFiles}`;
                    document.getElementById('totalCount').textContent = `剩余待标记: ${totalFiles}`;
                };

                onMounted(async () => {
                    totalFiles = await fetchTotalFiles();
                    completedFiles = await fetchcompletedFiles();
                    await fetchFile();
                    await updateStatus();
                });

                const fetchTotalFiles = async () => {
                    const response = await fetch('/get_total');
                    const data = await response.json();
                    return data.total_len;
                };

                const fetchcompletedFiles = async () => {
                    const response = await fetch('/get_completedFiles');
                    const data = await response.json();
                    return data.total_len;
                };

                return {
                    jsonContent,
                    selectedLanguage,
                    selectedEmotion,
                    selectedEvent,
                    selectedSemanticEmotion,  // 新增绑定
                    saveChanges,
                    updateTargetLength,
                    targetLength,
                    clearContent,
                };
            },
        }).mount('#app');
    </script>
</body>

</html>
