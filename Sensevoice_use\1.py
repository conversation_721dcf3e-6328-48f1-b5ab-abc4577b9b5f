from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess

model_dir = "./iic/SenseVoiceSmall"

# 加载第一个模型
# model1 = AutoModel(
#     model=model_dir1,  # 模型路径
#     trust_remote_code=True,  # 信任远程代码
#     remote_code="./model.py",  # 远程代码路径
#     device="cuda:0",  # 使用GPU加速
#     disable_update=True,  # 禁用更新
#     vad_model="iic/speech_fsmn_vad_zh-cn-16k-common-pytorch",  # 语音活动检测模型
#     vad_kwargs={"max_single_segment_time": 30000},  # VAD参数设置
#     ban_emo_unk=True,  # 禁止未知情感标签
#     output_token_probs=True  # 输出标记概率
# )

model = AutoModel(
    model=model_dir,
    # trust_remote_code=True,
    # remote_code="./model.py",
    vad_model="fsmn-vad",
    vad_kwargs={"max_single_segment_time": 30000},
    device="cuda:0",
    ban_emo_unk=True,  # 禁止未知情感标签
    output_token_probs=True  # 输出标记概率
)

# en
# res = model.generate(
#     input="./dkajdasjlkdj.WAV",
#     cache={},
#     language="zn",  # "zn", "en", "yue", "ja", "ko", "nospeech"
#     use_itn=True,
#     batch_size_s=60,
#     merge_vad=True,  #
#     merge_length_s=15,
# )

res = model.generate(input="./dkajdasjlkdj.WAV",
                           cache={},
                           language="zh",
                           use_itn=True,  # 使用ITN（逆文本规范化）
                           ban_emo_unk=True,  # 禁止未知情感标签
                           batch_size_s=60,  # 批处理大小
                           merge_vad=True)  # 合并VAD结果

# print(res)
text = rich_transcription_postprocess(res[0]["text"])
# emotion = res[0]["emotion"]
# print(emotion)
print(text)
