<!DOCTYPE html>
<html lang="zh" class="dark">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>音频上传与处理</title>
  <!-- 引入 Vue 3 -->
  <script src="https://unpkg.com/vue@3.2.47/dist/vue.global.prod.js"></script>
  <!-- 引入 ElementPlus -->
  <link rel="stylesheet" href="https://unpkg.com/element-plus@2.3.2/dist/index.css">
  <script src="https://unpkg.com/element-plus@2.3.2/dist/index.full.js"></script>
  <!-- 引入 Axios -->
  <script src="https://unpkg.com/axios/dist/axios.min.js"></script>

  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
      background-color: #f4f4f9;
    }

    h1 {
      color: #409EFF;
      text-align: center;
    }
    

    .audio-segment {
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 10px;
      background-color: #fafafa;
    }

    .audio-segment audio {
      flex-grow: 1;
      margin-top: 5px;
      max-width: 60%;
    }

    .audio-segment-info {
      display: flex;
      flex-direction: column;
      margin-left: 15px;
      flex-grow: 1;
    }

    .audio-segment-info span {
      margin-bottom: 5px;
      font-size: 14px;
    }

    .container {
      max-width: 100%;
      margin: 0 auto;
      padding: 20px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .aside_container {
      max-width: 100%;
      margin: 0 auto;
      margin-top: 20px;
      padding: 20px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .button-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20px;
    }

    .checkbox {
      margin-right: 15px;
      font-size: 18px;
    }

    .select-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20px;
    }

    .player-container {
      margin-top: 20px;
    }

    .player-container audio {
      width: 100%;
    }
  </style>
</head>

<body>

  <div id="app"></div>

  <script>
    const { createApp, ref } = Vue;
    const { ElButton, ElLoading, ElInputNumber, ElCheckboxGroup, ElCheckbox, ElMessage, ElDrawer, ElSelect, ElOption, ElInput } = ElementPlus;


    createApp({
      setup() {

        // 定义响应式数据
        const file = ref(null);
        const audioData = ref([]);
        const selectedSegments = ref([]);
        const selectedSpeaker = ref(""); // 用于存储选择的说话人（全选，1，2，...）
        const availableSpeakers = ref([]); // 存储实际数据中可选的说话人
        const selectedNumber = ref(2);
        const sentimentLabels = ['自然', '开心', '伤心', '生气', '害怕']; // 情感标签
        // 播放器的状态
        const playingSegment = ref(null); // 当前播放的音频片段
        const audioPlayer = ref(null); // 全局音频播放器
        const audioSrc = ref(""); // 用于存储选择的音频文件路径
        const drawerLoading = ref(false);
        // 侧边栏状态
        const drawerVisible = ref(false);
        const modifiedSegments = ref([]);
        const json_count = ref(0);

        const handleFileUpload = (uploadedFile) => {
          // file 是 el-upload 的上传文件对象
          const rawFile = uploadedFile.raw; // 获取原始文件对象

          // 存储文件
          file.value = rawFile;
          console.log(file.value);
          // 创建音频播放器并加载选中的文件
          const fileUrl = URL.createObjectURL(rawFile);
          audioSrc.value = fileUrl;
        };
        const submitAudio = async () => {
          console.log('提交前的文件:', file.value);
          if (!file.value) {
            ElMessage.warning('请上传音频文件');
            return;
          }

          // 创建 FormData 对象上传音频文件
          const formData = new FormData();
          formData.append('audio', file.value);
          formData.append('oracle_num', selectedNumber.value);

          // 显示 loading
          const loading = ElLoading.service({
            fullscreen: true,  // 全屏 loading
            text: '正在解析中，请稍候...',
            background: 'rgba(0, 0, 0, 0.7)' // 设置背景颜色
          });

          try {
            const response = await axios.post('http://*************:6001/upload_audio', formData, {
              headers: { 'Content-Type': 'multipart/form-data' },
            });

            // 假设服务器返回音频片段数据
            audioData.value = response.data.audioSegments;
            console.log(audioData.value);

            // 提取说话人列表
            const speakers = [...new Set(audioData.value.map(segment => segment.speaker))];
            availableSpeakers.value = speakers;
            selectedSpeaker.value = ''; // 清空选择
            selectedSegments.value = []; // 清空已选片段
            // 提交成功，关闭 loading
            loading.close();
          } catch (error) {
            console.error('音频上传失败', error);
            ElMessage.error('音频上传失败');

            // 上传失败，关闭 loading
            loading.close();
          }
        };
        const refreshData = async () => {
          const response = await axios.get('http://*************:6001/get_json_count');
          json_count.value = response.data.json_count;
        }
        
        // 格式化时间
        const formatTime = (seconds) => {
          const minutes = Math.floor(seconds / 60);
          const secs = Math.floor(seconds % 60);
          return `${minutes}:${secs < 10 ? '0' + secs : secs}`;
        };

        // 打开二级页面（侧边栏）
        const openDrawer = () => {
          console.log('打开侧边栏');
          modifiedSegments.value = selectedSegments.value.map(index => ({
            ...audioData.value[index],
            sentiment: sentimentLabels[0], // 默认情感为空
          }));
          drawerVisible.value = true;
        };

        // 提交修改后的数据
        const submitModifiedData = async () => {
          const loading2 = ElLoading.service({
            fullscreen: true,  // 全屏 loading
            text: '正在处理中，请稍候...',
            background: 'rgba(0, 0, 0, 0.7)' // 设置背景颜色
          });
          try {

            const response = await axios.post('http://*************:6001/submit_modified', modifiedSegments.value);
            ElMessage.success('修改提交成功');
            loading2.close()
            refreshData()
          } catch (error) {
            loading2.close()
            console.error('提交失败', error);
            ElMessage.error('提交失败');

          } finally {

          }
        };
        const toggleSelect = () => {
          if (selectedSpeaker.value === 'all') {
            // 取消全选
            selectedSpeaker.value = ''; // 清空选择
            selectedSegments.value = []; // 清空已选片段
          } else {
            // 全选
            selectedSpeaker.value = 'all'; // 设置为全选
            selectedSegments.value = audioData.value.map((_, index) => index); // 全选所有音频片段
          }
        };
        // 一键选择功能
        const handleSelect = () => {
          if (selectedSpeaker.value === "all") {
            selectedSegments.value = audioData.value.map((_, index) => index);
          } else {
            selectedSegments.value = audioData.value
              .map((segment, index) => segment.speaker === selectedSpeaker.value ? index : -1)
              .filter(index => index !== -1);
          }
        };
        const downloadJson = async () => {
          try {
            // 发送 GET 请求以获取 JSON 文件
            const timestamp = new Date().getTime();
            const get_json_file_url = `http://*************:6001/get_json_file?timestamp=${timestamp}`;
            const response = await axios.get(get_json_file_url, {
              responseType: 'blob', // 确保返回的是 Blob 数据
            });

            // 创建 Blob 对象
            const blob = new Blob([response.data], { type: 'application/json' });

            // 创建临时下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'data.json'; // 设置默认下载文件名
            document.body.appendChild(a); // 将链接添加到 DOM 中
            a.click(); // 触发点击事件
            document.body.removeChild(a); // 移除链接
            window.URL.revokeObjectURL(url); // 释放 URL 对象

          } catch (error) {
            console.error('下载失败:', error);
          }
        };
        const handleClearData = async () => {
          try {
            // 发送 GET 请求以获取 JSON 文件
            const response = await axios.post('http://*************:6001/clear_json');
            ElMessage.success('清除成功');
            refreshData();
          } catch (error) {
            console.error('下载失败:', error);
            ElMessage.success('清除失败');
            refreshData();
          }
        };

        const uploadUrl = 'http://*************:6001/speech_separation';  // 修改为您的服务器地址
        const se_file = ref(null);

        const se_handleFileChange = (se_file) => {
         
          se_file.value = se_file.raw; // 获取原始文件
        
        };

        const se_handleUploadSuccess = async (response, se_file, fileList) => {
              try {
                const loading3 = ElLoading.service({
                  fullscreen: true,  // 全屏 loading
                  text: '正在解析中，请稍候...',
                  background: 'rgba(0, 0, 0, 0.7)' // 设置背景颜色
                });
                // 创建 FormData 对象
                const formData = new FormData();
                formData.append('file', se_file.value);  // 'file' 应该是后端期望的字段名

                // 发送请求
                const res = await axios.post(uploadUrl, formData, {
                  headers: { 
                    'Content-Type': 'multipart/form-data' // 确保设置为 multipart/form-data
                  },
                  responseType: 'blob', // 设置响应类型为 blob
                });

                console.log('响应状态:', res.status);
                console.log('响应数据类型:', res.data);

                if (res.status === 200) {
                  // 直接获取 Blob 对象并生成下载链接
                  const blob = res.data;  // res.data 是一个 Blob 对象
                  const url = window.URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = "audio_files.zip";  // 设置下载文件名
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                  window.URL.revokeObjectURL(url); // 清理 URL 对象
                  loading3.close()
                } else {
                  ElMessage.error('下载失败');
                  console.error('下载失败，响应状态:', res.status);
                  loading.close()
                }
              } catch (error) {
                
                ElMessage.error('下载失败');
                console.error('下载失败', error);
                loading.close()
              }
            };
        // 上传失败后处理
        const se_handleUploadError = (err, se_file, fileList) => {
          ElMessage.error('上传失败');
          console.error('上传失败', err);
        };

        return {
          file,
          uploadUrl,
          se_file,
          downloadJson,
          handleClearData,
          se_handleFileChange,
          se_handleUploadSuccess,
          se_handleUploadError,
          audioData,
          refreshData,
          json_count,
          selectedNumber,
          selectedSegments,
          selectedSpeaker,
          availableSpeakers,
          audioSrc,
          drawerVisible,
          drawerLoading,
          modifiedSegments,
          sentimentLabels,
          handleFileUpload,
          submitAudio,
          formatTime,
          openDrawer,
          submitModifiedData,
          handleSelect,
          toggleSelect
        };
      },
      components: {
        ElButton,
        ElInputNumber,
        ElCheckboxGroup,
        ElCheckbox,
        ElDrawer,
        ElSelect,
        ElOption,
        ElInput,
      },
      template: `
   <div class="common-layout">
    <el-container>

        <el-aside width="250px">
            <div class="aside_container">
                <el-row>
                    <el-col>
                        <div class="statistic-container">
                            <el-statistic title="数据存量" :value="json_count" /><br>
                            <el-button type="primary" @click="refreshData" class="refresh-button">刷新</el-button><br><br>
                            <el-button type="primary" @click="downloadJson" class="refresh-button">下载数据</el-button>
                            <el-popconfirm title="确定清空吗？" confirm-button-text="确定" cancel-button-text="取消"
                                @confirm="handleClearData">
                                <template #reference>
                                    <el-button type="danger">清空数据</el-button>
                                </template>
                            </el-popconfirm>
                        </div>

                    </el-col>
                </el-row>
            </div>
            <div class="aside_container">
                  <div>
                  <!-- 音频上传 -->
                  <h4  style="margin: 10px;">音频分离</h4>
                  <br>
                  <el-upload
                    :action="uploadUrl"
                    :auto-upload="true"
                    accept="audio/*"
                    :show-file-list="false"
                    :on-change="se_handleFileChange"
                    :on-success="se_handleUploadSuccess"
                    :on-error="se_handleUploadError"
                  >
                    <el-button type="primary" icon="el-icon-upload">上传音频</el-button>
                  </el-upload>
                </div>
            </div>
            
        </el-aside>
        <el-main>
            <div class="container">
                <h1>音频处理</h1>

                <!-- 音频上传 -->
                <div>
                    <el-upload :auto-upload="false" accept="audio/*" :show-file-list="false"
                        :on-change="handleFileUpload">
                        <el-button type="primary" icon="el-icon-upload">上传音频</el-button>
                    </el-upload>

                </div>

                <!-- 播放器 -->
                <div v-if="audioSrc" class="player-container">
                    <h2>上传音频播放</h2>
                    <audio :src="audioSrc" controls></audio>
                </div>

                <!-- 数字框 -->
                <div class="select-container" v-if="audioSrc">
                    <div>
                        <span>预估说话人数：</span>
                        <el-input-number v-model="selectedNumber" :min="2" :max="10"></el-input-number>
                    </div>
                </div>
                <div class="select-container" v-if="audioSrc">
                    <ElButton type="primary" @click="submitAudio">提交音频</ElButton>
                </div>
                <!-- 一键选择功能 -->
                <div class="select-container" v-if="audioData.length">
                    <div>
                        <span>说话人音频选择：</span>
                        <el-button type="primary" @click="toggleSelect">
                            {{ selectedSpeaker === 'all' ? '取消全选' : '全选' }}
                        </el-button>
                        <ElButton v-for="speaker in availableSpeakers" :key="speaker" type="primary"
                            @click="selectedSpeaker = speaker; handleSelect()">
                            说话人 {{ speaker }}
                        </ElButton>
                    </div>
                </div>
                <!-- 音频片段显示 -->
                <div v-if="audioData.length">
                    <h2>音频片段</h2>
                    <ul>
                        <li v-for="(segment, index) in audioData" :key="index" class="audio-segment">
                            <div class="checkbox">
                                <ElCheckbox v-model="selectedSegments" :label="index" size="large"></ElCheckbox>
                            </div>
                            <div class="audio-segment-info">
                                <span>{{ formatTime(segment.start) }} - {{ formatTime(segment.end) }} | 说话人: {{
                                    segment.speaker }}</span>
                                <audio :src="segment.audioUrl" controls></audio>
                                <p>{{ segment.transcription }}</p>
                            </div>
                        </li>
                    </ul>
                    <div class="button-container">
                        <ElButton type="primary" @click="openDrawer">开始音频处理</ElButton>
                    </div>
                </div>
            </div>
        </el-main>
        <!-- 侧边栏（二级页面） -->
        <ElDrawer v-model="drawerVisible" title="修改音频片段" size="40%" :close-on-click-modal="false"
            @close="drawerVisible = false">
            <div v-for="(segment, index) in modifiedSegments" :key="index" class="audio-segment">
                <div class="audio-segment-info">
                    <span>{{ formatTime(segment.start) }} - {{ formatTime(segment.end) }} | 说话人: {{ segment.speaker
                        }}</span>
                    <audio :src="segment.audioUrl" controls></audio>
                    <p>
                        <el-input autosize  type="textarea" v-model="segment.transcription" placeholder="修改文字内容"></el-input>
                    </p>
                    <el-select v-model="segment.sentiment" placeholder="选择情感">
                        <el-option v-for="label in sentimentLabels" :key="label" :label="label"
                            :value="label"></el-option>
                    </el-select>
                </div>
            </div>
            <div class="button-container">
                <ElButton type="primary" @click="submitModifiedData">提交修改</ElButton>
            </div>
        </ElDrawer>
        <el-backtop :right="100" :bottom="100" />

    </el-container>
</div>
    `,
    }).use(ElementPlus).mount('#app');
  </script>

</body>

</html>