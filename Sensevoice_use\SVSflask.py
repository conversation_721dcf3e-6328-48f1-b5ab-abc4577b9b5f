import os
import time
from flask import Flask, request, jsonify
from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess
from werkzeug.utils import secure_filename

# 初始化 Flask 应用
app = Flask(__name__)




# 加载模型
model = AutoModel(
    model="./iic/SenseVoiceSmall",
    vad_model="fsmn-vad",
    vad_kwargs={"max_single_segment_time": 30000},
    device="cuda:0",
)





@app.route("/asr/file", methods=["POST"])
def asr_file():
    if "file" not in request.files:
        return jsonify({"code": 1, "message": "No file uploaded", "text": None}), 400

    file = request.files["file"]
    filename = secure_filename(file.filename)

    if not filename.lower().endswith((".wav", ".mp3", ".flac", ".ogg")):
        return jsonify({"code": 1, "message": "Unsupported audio format", "text": None}), 400

    try:
        s = time.time()
        tmp_file = f"tmp_{filename}"
        file.save(tmp_file)

        # 推理
        res = model.generate(
            input=tmp_file,
            cache={},
            language="zh",
            use_itn=True,
            batch_size_s=60,
            merge_vad=True,
            merge_length_s=15
        )
        os.remove(tmp_file)

        text = rich_transcription_postprocess(res[0])
        e = time.time()
        print(f"{(e - s) * 1000:.2f}ms")
        print(text)

        return jsonify({"code": 0, "message": "ok", "text": text})
    except Exception as e:
        return jsonify({"code": 1, "message": str(e), "text": None}), 500


@app.route("/health", methods=["GET"])
def health():
    return jsonify({"status": "healthy"})


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=34578)
