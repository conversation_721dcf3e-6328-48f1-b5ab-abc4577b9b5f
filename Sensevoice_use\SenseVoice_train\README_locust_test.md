# SenseVoice 并发性能测试指南

## 测试工具介绍

我们使用 Locust 作为压力测试工具。Locust 是一个易于使用的、分布式的、用户负载测试工具，专为测试网站（或其他系统）的并发能力而设计。

## 环境准备

1. 首先需要安装 Locust：

```bash
pip install locust
```

2. 确保 SenseVoice 数据服务器正在运行：

```bash
python dataserver_sqlite.py
```

## 测试脚本说明

测试脚本 `locust_test.py` 模拟了用户在 SenseVoice 数据标注系统中的行为：

- **访问主页**：模拟用户打开系统主页
- **获取任务**：模拟用户获取标注任务
- **提交任务**：模拟用户完成并提交标注任务
- **查看统计信息**：模拟用户查看待处理和已完成任务数量

## 运行测试

运行以下命令启动测试：

```bash
locust -f locust_test.py --host=http://服务器IP:5002
```

或者指定用户数和孵化速率：

```bash
locust -f locust_test.py --host=http://服务器IP:5002 --users 100 --spawn-rate 10
```

参数说明：
- `--users 100`：模拟100个并发用户
- `--spawn-rate 10`：每秒增加10个用户，直到达到指定用户数

## 查看测试结果

1. 启动测试后，访问 http://localhost:8089 打开 Locust Web 界面
2. 在界面中设置用户数量和孵化速率（如果不是通过命令行参数指定）
3. 点击 "Start swarming" 开始测试
4. 实时查看请求统计、响应时间图表和失败请求

## 测试指标解读

- **RPS (Requests Per Second)**：每秒请求数，表示系统处理请求的能力
- **响应时间**：请求从发送到接收响应的时间，包括：
  - **Average**：平均响应时间
  - **Min**：最小响应时间
  - **Max**：最大响应时间
  - **Median**：中位数响应时间
  - **90%ile/95%ile/99%ile**：百分比响应时间
- **失败率**：请求失败的百分比

## 常见问题排查

1. **响应时间过长**：
   - 检查数据库操作是否有优化空间
   - 检查是否有长时间运行的查询
   - 考虑增加数据库连接池

2. **错误率高**：
   - 查看服务器日志了解具体错误
   - 检查是否存在资源竞争问题
   - 检查数据库锁问题

3. **请求吞吐量低**：
   - 考虑增加服务器资源
   - 检查是否有可以优化的代码
   - 考虑使用异步处理

## 优化建议

1. 对于高并发场景，可以考虑：
   - 使用连接池管理数据库连接
   - 实现更细粒度的锁机制
   - 采用异步处理非关键路径操作

2. 针对响应时间：
   - 优化数据库查询
   - 考虑使用缓存
   - 减少不必要的数据传输

## 注意事项

- 测试前确保数据库中有足够的测试数据
- 测试环境应尽量接近生产环境
- 建议先从小规模测试开始，逐步增加用户数 