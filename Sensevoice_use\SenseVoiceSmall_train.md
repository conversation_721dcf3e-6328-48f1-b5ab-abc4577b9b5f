### 部署sensevoicesmall

命令行下载

下载到指定本地文件夹

```
modelscope download --model iic/SenseVoiceSmall  --local_dir ./dir
```

SDK下载

```
from modelscope import snapshot_download

model_dir = snapshot_download(
    repo_id='iic/SenseVoiceSmall',  # e.g. 'damo/SenseVoiceSmall'
    cache_dir=''  # 可选：指定本地缓存目录
```

### 模型返回值

```json
[{'key': 'dkajdasjlkdj', 'text': '<|zh|><|NEUTRAL|><|Speech|><|withitn|>留任了，其实干的事情是没有大姨多的。嗯，对比一下大衣上的话，不对，应该是说大一阶段现在感觉还是可以的，对比一下的话嗯。 <|zh|><|NEUTRAL|><|Speech|><|withitn|>嗯，那哎我看一看，就是就是最近。 <|zh|><|NEUTRAL|><|Speech|><|withitn|>你在做那些哎，就是你不是说到好像要什么小组分工，你不是组长，但是莫名承担了这个分工的责任的那个事情吗？ <|zh|><|NEUTRAL|><|Speech|><|withitn|>那个有那么快到DDL吗？啊，其实是就是他思政课时学的是有两次，第一次的时候一开始其是另外一个同学，他是队啥，然后呢。 <|zh|><|NEUTRAL|><|Speech|><|withitn|>我不我不知道怎么说，他可能在某些方面比我顺恐点，但是在某些方面比我顺牛一点，我也不知道怎么说。总之在那那件总之后面不知道为什么，就是我来分了。然后后面就然后所以这次要做的时候，然后就是一一呃就是其中两个队员是我的舍友，然后他们就问我。'}]

```

<|zh|>:语言

<|NEUTRAL|>：情绪

<|Speech|>：事件

<|withitn|>

### 使用funasr训练sensevoicesmall

## 微调



### 安装训练环境



```
git clone https://github.com/alibaba/FunASR.git && cd FunASR
pip3 install -e ./
```



### 数据准备



数据格式需要包括如下几个字段：

```text
{"key": "YOU0000008470_S0000238_punc_itn", 
 "text_language": "<|en|>", 
 "emo_target": "<|NEUTRAL|>", 
 "event_target": "<|Speech|>", 
 "with_or_wo_itn": "<|withitn|>",
 "target": "Including legal due diligence, subscription agreement, negotiation.", 
 "source":"/cpfs01/shared/Group-speech/beinian.lzr/data/industrial_data/english_all/audio/YOU0000008470_S0000238.wav", 
 "target_len": 7,
 "source_len": 140}
{"key": "AUD0000001556_S0007580", "text_language": "<|en|>", "emo_target": "<|NEUTRAL|>", "event_target": "<|Speech|>", "with_or_wo_itn": "<|woitn|>", "target": "there is a tendency to identify the self or take interest in what one has got used to", "source": "/cpfs01/shared/Group-speech/beinian.lzr/data/industrial_data/english_all/audio/AUD0000001556_S0007580.wav", "target_len": 18, "source_len": 360}
```

解释：

- key：数据集中当前数据项的唯一标识符，一般与音频文件的名称相同，即source标签中的文件名。

- source：音频数据源的存放路径。
- source_len：音频中的有效信息长度，10ms为单位，见阿里云社区解释。
- target：音频文件中对应的文本信息。
- target_len：对应文字信息的长度，如：“hello world”对应的target_len=2；中文每个字对应1个，如“你好”对应target_len=2。标点不计入target_len。
- “text_language”：英语<|en|>、普通话<|zh|>、粤语<|yue|>、日语<|jp|>、韩语<|ko|>
- “emo_target”：<|NEUTRAL|>、<|EMO_UNKNOWN|>、<|HAPPY|>、<|SAD|>、<|ANGRY|>
- “event_target”：<|Speech|> 表示这是一个语音事件。
- “with_or_wo_itn”：是否包括标点符号和逆文本规范化。<|withitn|>表示文本内容中有“逆文本规范化”或“有标点符号”，<|woitn|>: 表示文本内容没有经过逆文本规范化处理且没有标点符号，保持原始形式。

详细可以参考：`data/train_example.jsonl`

<details open="" style="box-sizing: border-box; display: block; margin-top: 0px; margin-bottom: 16px; color: rgb(31, 35, 40); font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;Noto Sans&quot;, Helvetica, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;; font-size: 16px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;"><summary style="box-sizing: border-box; display: list-item; cursor: pointer; box-shadow: none; outline: none;">数据准备细节介绍</summary><ul dir="auto" style="box-sizing: border-box; padding-left: 2em; margin-top: 0px; margin-bottom: 16px;"><li style="box-sizing: border-box;"><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">key</code>: 数据唯一 ID</li><li style="box-sizing: border-box; margin-top: 0.25em;"><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">source</code>：音频文件的路径</li><li style="box-sizing: border-box; margin-top: 0.25em;"><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">source_len</code>：音频文件的 fbank 帧数</li><li style="box-sizing: border-box; margin-top: 0.25em;"><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">target</code>：音频文件标注文本</li><li style="box-sizing: border-box; margin-top: 0.25em;"><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">target_len</code>：音频文件标注文本长度</li><li style="box-sizing: border-box; margin-top: 0.25em;"><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">text_language</code>：音频文件的语种标签</li><li style="box-sizing: border-box; margin-top: 0.25em;"><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">emo_target</code>：音频文件的情感标签</li><li style="box-sizing: border-box; margin-top: 0.25em;"><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">event_target</code>：音频文件的事件标签</li><li style="box-sizing: border-box; margin-top: 0.25em;"><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">with_or_wo_itn</code>：标注文本中是否包含标点与逆文本正则化</li></ul><p dir="auto" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 16px;">可以用指令<span>&nbsp;</span><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">sensevoice2jsonl</code><span>&nbsp;</span>从 train_wav.scp、train_text.txt、train_text_language.txt、train_emo_target.txt 和 train_event_target.txt 生成，准备过程如下：</p><p dir="auto" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 16px;"><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">train_text.txt</code></p><p dir="auto" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 16px;">左边为数据唯一 ID，需与<span>&nbsp;</span><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">train_wav.scp</code><span>&nbsp;</span>中的<span>&nbsp;</span><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">ID</code><span>&nbsp;</span>一一对应 右边为音频文件标注文本，格式如下：</p><div class="highlight highlight-source-shell notranslate position-relative overflow-auto" dir="auto" style="box-sizing: border-box; position: relative !important; overflow: auto !important; margin-bottom: 16px; display: flex; justify-content: space-between; background-color: rgb(246, 248, 250);"><pre style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; margin-top: 0px; margin-bottom: 0px; overflow-wrap: normal; padding: 16px; overflow: auto; line-height: 1.45; color: rgb(31, 35, 40); background-color: rgb(246, 248, 250); border-radius: 6px; word-break: normal; min-height: 52px;">BAC009S0764W0121 甚至出现交易几乎停滞的情况
BAC009S0916W0489 湖北一公司以员工名义贷款数十员工负债千万
asr_example_cn_en 所有只要处理 data 不管你是做 machine learning 做 deep learning 做 data analytics 做 data science 也好 scientist 也好通通都要都做的基本功啊那 again 先先对有一些 <span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&gt;</span> 也许对
ID0012W0014 he tried to think how it could be</pre><div class="zeroclipboard-container" style="box-sizing: border-box; display: block; animation: auto ease 0s 1 normal none running none;"><clipboard-copy aria-label="Copy" class="ClipboardButton btn btn-invisible js-clipboard-copy m-2 p-0 d-flex flex-justify-center flex-items-center" data-copy-feedback="Copied!" data-tooltip-direction="w" value="BAC009S0764W0121 甚至出现交易几乎停滞的情况
BAC009S0916W0489 湖北一公司以员工名义贷款数十员工负债千万
asr_example_cn_en 所有只要处理 data 不管你是做 machine learning 做 deep learning 做 data analytics 做 data science 也好 scientist 也好通通都要都做的基本功啊那 again 先先对有一些 > 也许对
ID0012W0014 he tried to think how it could be" tabindex="0" role="button" style="box-sizing: border-box; position: relative; display: flex !important; padding: 0px !important; font-size: 14px; font-weight: 500; line-height: 20px; white-space: nowrap; vertical-align: middle; cursor: pointer; user-select: none; border: 0px; border-radius: 6px; appearance: none; color: rgb(9, 105, 218); background-color: transparent; box-shadow: none; transition: color 80ms cubic-bezier(0.33, 1, 0.68, 1), background-color, box-shadow, border-color; justify-content: center !important; align-items: center !important; margin: 8px !important; width: 28px; height: 28px;"><svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-copy js-clipboard-copy-icon"><path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path><path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path></svg></clipboard-copy></div></div><p dir="auto" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 16px;"><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">train_wav.scp</code></p><p dir="auto" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 16px;">左边为数据唯一 ID，需与<span>&nbsp;</span><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">train_text.txt</code><span>&nbsp;</span>中的<span>&nbsp;</span><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">ID</code><span>&nbsp;</span>一一对应 右边为音频文件的路径，格式如下</p><div class="highlight highlight-source-shell notranslate position-relative overflow-auto" dir="auto" style="box-sizing: border-box; position: relative !important; overflow: auto !important; margin-bottom: 16px; display: flex; justify-content: space-between; background-color: rgb(246, 248, 250);"><pre style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; margin-top: 0px; margin-bottom: 0px; overflow-wrap: normal; padding: 16px; overflow: auto; line-height: 1.45; color: rgb(31, 35, 40); background-color: rgb(246, 248, 250); border-radius: 6px; word-break: normal; min-height: 52px;">BAC009S0764W0121 https://isv-data.oss-cn-hangzhou.aliyuncs.com/ics/MaaS/ASR/test_audio/BAC009S0764W0121.wav
BAC009S0916W0489 https://isv-data.oss-cn-hangzhou.aliyuncs.com/ics/MaaS/ASR/test_audio/BAC009S0916W0489.wav
asr_example_cn_en https://isv-data.oss-cn-hangzhou.aliyuncs.com/ics/MaaS/ASR/test_audio/asr_example_cn_en.wav
ID0012W0014 https://isv-data.oss-cn-hangzhou.aliyuncs.com/ics/MaaS/ASR/test_audio/asr_example_en.wav</pre><div class="zeroclipboard-container" style="box-sizing: border-box; display: block; animation: auto ease 0s 1 normal none running none;"><clipboard-copy aria-label="Copy" class="ClipboardButton btn btn-invisible js-clipboard-copy m-2 p-0 d-flex flex-justify-center flex-items-center" data-copy-feedback="Copied!" data-tooltip-direction="w" value="BAC009S0764W0121 https://isv-data.oss-cn-hangzhou.aliyuncs.com/ics/MaaS/ASR/test_audio/BAC009S0764W0121.wav
BAC009S0916W0489 https://isv-data.oss-cn-hangzhou.aliyuncs.com/ics/MaaS/ASR/test_audio/BAC009S0916W0489.wav
asr_example_cn_en https://isv-data.oss-cn-hangzhou.aliyuncs.com/ics/MaaS/ASR/test_audio/asr_example_cn_en.wav
ID0012W0014 https://isv-data.oss-cn-hangzhou.aliyuncs.com/ics/MaaS/ASR/test_audio/asr_example_en.wav" tabindex="0" role="button" style="box-sizing: border-box; position: relative; display: flex !important; padding: 0px !important; font-size: 14px; font-weight: 500; line-height: 20px; white-space: nowrap; vertical-align: middle; cursor: pointer; user-select: none; border: 0px; border-radius: 6px; appearance: none; color: rgb(9, 105, 218); background-color: transparent; box-shadow: none; transition: color 80ms cubic-bezier(0.33, 1, 0.68, 1), background-color, box-shadow, border-color; justify-content: center !important; align-items: center !important; margin: 8px !important; width: 28px; height: 28px;"><svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-copy js-clipboard-copy-icon"><path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path><path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path></svg></clipboard-copy></div></div><p dir="auto" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 16px;"><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">train_text_language.txt</code></p><p dir="auto" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 16px;">左边为数据唯一 ID，需与<span>&nbsp;</span><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">train_text_language.txt</code><span>&nbsp;</span>中的<span>&nbsp;</span><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">ID</code><span>&nbsp;</span>一一对应 右边为音频文件的语种标签，支持<span>&nbsp;</span><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|zh|&gt;</code>、<code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|en|&gt;</code>、<code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|yue|&gt;</code>、<code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|ja|&gt;</code><span>&nbsp;</span>和<span>&nbsp;</span><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|ko|&gt;</code>，格式如下</p><div class="highlight highlight-source-shell notranslate position-relative overflow-auto" dir="auto" style="box-sizing: border-box; position: relative !important; overflow: auto !important; margin-bottom: 16px; display: flex; justify-content: space-between; background-color: rgb(246, 248, 250);"><pre style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; margin-top: 0px; margin-bottom: 0px; overflow-wrap: normal; padding: 16px; overflow: auto; line-height: 1.45; color: rgb(31, 35, 40); background-color: rgb(246, 248, 250); border-radius: 6px; word-break: normal; min-height: 52px;">BAC009S0764W0121 <span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&lt;</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span>zh<span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&gt;</span>
BAC009S0916W0489 <span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&lt;</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span>zh<span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&gt;</span>
asr_example_cn_en <span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&lt;</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span>zh<span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&gt;</span>
ID0012W0014 <span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&lt;</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span>en<span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&gt;</span></pre><div class="zeroclipboard-container" style="box-sizing: border-box; display: block; animation: auto ease 0s 1 normal none running none;"><clipboard-copy aria-label="Copy" class="ClipboardButton btn btn-invisible js-clipboard-copy m-2 p-0 d-flex flex-justify-center flex-items-center" data-copy-feedback="Copied!" data-tooltip-direction="w" value="BAC009S0764W0121 <|zh|>
BAC009S0916W0489 <|zh|>
asr_example_cn_en <|zh|>
ID0012W0014 <|en|>" tabindex="0" role="button" style="box-sizing: border-box; position: relative; display: flex !important; padding: 0px !important; font-size: 14px; font-weight: 500; line-height: 20px; white-space: nowrap; vertical-align: middle; cursor: pointer; user-select: none; border: 0px; border-radius: 6px; appearance: none; color: rgb(9, 105, 218); background-color: transparent; box-shadow: none; transition: color 80ms cubic-bezier(0.33, 1, 0.68, 1), background-color, box-shadow, border-color; justify-content: center !important; align-items: center !important; margin: 8px !important; width: 28px; height: 28px;"><svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-copy js-clipboard-copy-icon"><path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path><path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path></svg></clipboard-copy></div></div><p dir="auto" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 16px;"><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">train_emo.txt</code></p><p dir="auto" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 16px;">左边为数据唯一 ID，需与<span>&nbsp;</span><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">train_emo.txt</code><span>&nbsp;</span>中的<span>&nbsp;</span><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">ID</code><span>&nbsp;</span>一一对应 右边为音频文件的情感标签，支持<span>&nbsp;</span><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|HAPPY|&gt;</code>、<code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|SAD|&gt;</code>、<code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|ANGRY|&gt;</code>、<code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|NEUTRAL|&gt;</code>、<code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|FEARFUL|&gt;</code>、<code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|DISGUSTED|&gt;</code><span>&nbsp;</span>和<span>&nbsp;</span><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|SURPRISED|&gt;</code>，格式如下</p><div class="highlight highlight-source-shell notranslate position-relative overflow-auto" dir="auto" style="box-sizing: border-box; position: relative !important; overflow: auto !important; margin-bottom: 16px; display: flex; justify-content: space-between; background-color: rgb(246, 248, 250);"><pre style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; margin-top: 0px; margin-bottom: 0px; overflow-wrap: normal; padding: 16px; overflow: auto; line-height: 1.45; color: rgb(31, 35, 40); background-color: rgb(246, 248, 250); border-radius: 6px; word-break: normal; min-height: 52px;">BAC009S0764W0121 <span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&lt;</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span>NEUTRAL<span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&gt;</span>
BAC009S0916W0489 <span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&lt;</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span>NEUTRAL<span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&gt;</span>
asr_example_cn_en <span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&lt;</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span>NEUTRAL<span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&gt;</span>
ID0012W0014 <span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&lt;</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span>NEUTRAL<span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&gt;</span></pre><div class="zeroclipboard-container" style="box-sizing: border-box; display: block; animation: auto ease 0s 1 normal none running none;"><clipboard-copy aria-label="Copy" class="ClipboardButton btn btn-invisible js-clipboard-copy m-2 p-0 d-flex flex-justify-center flex-items-center" data-copy-feedback="Copied!" data-tooltip-direction="w" value="BAC009S0764W0121 <|NEUTRAL|>
BAC009S0916W0489 <|NEUTRAL|>
asr_example_cn_en <|NEUTRAL|>
ID0012W0014 <|NEUTRAL|>" tabindex="0" role="button" style="box-sizing: border-box; position: relative; display: flex !important; padding: 0px !important; font-size: 14px; font-weight: 500; line-height: 20px; white-space: nowrap; vertical-align: middle; cursor: pointer; user-select: none; border: 0px; border-radius: 6px; appearance: none; color: rgb(9, 105, 218); background-color: transparent; box-shadow: none; transition: color 80ms cubic-bezier(0.33, 1, 0.68, 1), background-color, box-shadow, border-color; justify-content: center !important; align-items: center !important; margin: 8px !important; width: 28px; height: 28px;"><svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-copy js-clipboard-copy-icon"><path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path><path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path></svg></clipboard-copy></div></div><p dir="auto" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 16px;"><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">train_event.txt</code></p><p dir="auto" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 16px;">左边为数据唯一 ID，需与<span>&nbsp;</span><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">train_event.txt</code><span>&nbsp;</span>中的<span>&nbsp;</span><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">ID</code><span>&nbsp;</span>一一对应 右边为音频文件的事件标签，支持<span>&nbsp;</span><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|BGM|&gt;</code>、<code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|Speech|&gt;</code>、<code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|Applause|&gt;</code>、<code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|Laughter|&gt;</code>、<code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|Cry|&gt;</code>、<code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|Sneeze|&gt;</code>、<code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|Breath|&gt;</code><span>&nbsp;</span>和<span>&nbsp;</span><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">&lt;|Cough|&gt;</code>，格式如下</p><div class="highlight highlight-source-shell notranslate position-relative overflow-auto" dir="auto" style="box-sizing: border-box; position: relative !important; overflow: auto !important; margin-bottom: 16px; display: flex; justify-content: space-between; background-color: rgb(246, 248, 250);"><pre style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; margin-top: 0px; margin-bottom: 0px; overflow-wrap: normal; padding: 16px; overflow: auto; line-height: 1.45; color: rgb(31, 35, 40); background-color: rgb(246, 248, 250); border-radius: 6px; word-break: normal; min-height: 52px;">BAC009S0764W0121 <span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&lt;</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span>Speech<span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&gt;</span>
BAC009S0916W0489 <span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&lt;</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span>Speech<span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&gt;</span>
asr_example_cn_en <span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&lt;</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span>Speech<span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&gt;</span>
ID0012W0014 <span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&lt;</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span>Speech<span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">|</span><span class="pl-k" style="box-sizing: border-box; color: rgb(207, 34, 46);">&gt;</span></pre><div class="zeroclipboard-container" style="box-sizing: border-box; display: block; animation: auto ease 0s 1 normal none running none;"><clipboard-copy aria-label="Copy" class="ClipboardButton btn btn-invisible js-clipboard-copy m-2 p-0 d-flex flex-justify-center flex-items-center" data-copy-feedback="Copied!" data-tooltip-direction="w" value="BAC009S0764W0121 <|Speech|>
BAC009S0916W0489 <|Speech|>
asr_example_cn_en <|Speech|>
ID0012W0014 <|Speech|>" tabindex="0" role="button" style="box-sizing: border-box; position: relative; display: flex !important; padding: 0px !important; font-size: 14px; font-weight: 500; line-height: 20px; white-space: nowrap; vertical-align: middle; cursor: pointer; user-select: none; border: 0px; border-radius: 6px; appearance: none; color: rgb(9, 105, 218); background-color: transparent; box-shadow: none; transition: color 80ms cubic-bezier(0.33, 1, 0.68, 1), background-color, box-shadow, border-color; justify-content: center !important; align-items: center !important; margin: 8px !important; width: 28px; height: 28px;"><svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-copy js-clipboard-copy-icon"><path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path><path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path></svg></clipboard-copy></div></div><p dir="auto" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 16px;"><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">生成指令</code></p><div class="highlight highlight-source-shell notranslate position-relative overflow-auto" dir="auto" style="box-sizing: border-box; position: relative !important; overflow: auto !important; margin-bottom: 16px; display: flex; justify-content: space-between; background-color: rgb(246, 248, 250);"><pre style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; margin-top: 0px; margin-bottom: 0px; overflow-wrap: normal; padding: 16px; overflow: auto; line-height: 1.45; color: rgb(31, 35, 40); background-color: rgb(246, 248, 250); border-radius: 6px; word-break: normal; min-height: 52px;"><span class="pl-c" style="box-sizing: border-box; color: rgb(89, 99, 110);"><span class="pl-c" style="box-sizing: border-box; color: rgb(89, 99, 110);">#</span> generate train.jsonl and val.jsonl from wav.scp, text.txt, text_language.txt, emo_target.txt, event_target.txt</span>
sensevoice2jsonl \
++scp_file_list=<span class="pl-s" style="box-sizing: border-box; color: rgb(10, 48, 105);"><span class="pl-pds" style="box-sizing: border-box; color: rgb(10, 48, 105);">'</span>["../../../data/list/train_wav.scp", "../../../data/list/train_text.txt", "../../../data/list/train_text_language.txt", "../../../data/list/train_emo.txt", "../../../data/list/train_event.txt"]<span class="pl-pds" style="box-sizing: border-box; color: rgb(10, 48, 105);">'</span></span> \
++data_type_list=<span class="pl-s" style="box-sizing: border-box; color: rgb(10, 48, 105);"><span class="pl-pds" style="box-sizing: border-box; color: rgb(10, 48, 105);">'</span>["source", "target", "text_language", "emo_target", "event_target"]<span class="pl-pds" style="box-sizing: border-box; color: rgb(10, 48, 105);">'</span></span> \
++jsonl_file_out=<span class="pl-s" style="box-sizing: border-box; color: rgb(10, 48, 105);"><span class="pl-pds" style="box-sizing: border-box; color: rgb(10, 48, 105);">"</span>../../../data/list/train.jsonl<span class="pl-pds" style="box-sizing: border-box; color: rgb(10, 48, 105);">"</span></span></pre><div class="zeroclipboard-container" style="box-sizing: border-box; display: block; animation: auto ease 0s 1 normal none running none;"><clipboard-copy aria-label="Copy" class="ClipboardButton btn btn-invisible js-clipboard-copy m-2 p-0 d-flex flex-justify-center flex-items-center" data-copy-feedback="Copied!" data-tooltip-direction="w" value="# generate train.jsonl and val.jsonl from wav.scp, text.txt, text_language.txt, emo_target.txt, event_target.txt
sensevoice2jsonl \
++scp_file_list='[&quot;../../../data/list/train_wav.scp&quot;, &quot;../../../data/list/train_text.txt&quot;, &quot;../../../data/list/train_text_language.txt&quot;, &quot;../../../data/list/train_emo.txt&quot;, &quot;../../../data/list/train_event.txt&quot;]' \
++data_type_list='[&quot;source&quot;, &quot;target&quot;, &quot;text_language&quot;, &quot;emo_target&quot;, &quot;event_target&quot;]' \
++jsonl_file_out=&quot;../../../data/list/train.jsonl&quot;" tabindex="0" role="button" style="box-sizing: border-box; position: relative; display: flex !important; padding: 0px !important; font-size: 14px; font-weight: 500; line-height: 20px; white-space: nowrap; vertical-align: middle; cursor: pointer; user-select: none; border: 0px; border-radius: 6px; appearance: none; color: rgb(9, 105, 218); background-color: transparent; box-shadow: none; transition: color 80ms cubic-bezier(0.33, 1, 0.68, 1), background-color, box-shadow, border-color; justify-content: center !important; align-items: center !important; margin: 8px !important; width: 28px; height: 28px;"><svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-copy js-clipboard-copy-icon"><path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path><path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path></svg></clipboard-copy></div></div><p dir="auto" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 16px;">若无 train_text_language.txt、train_emo_target.txt 和 train_event_target.txt，则自动通过使用<span>&nbsp;</span><code style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; padding: 0.2em 0.4em; margin: 0px; white-space: break-spaces; background-color: rgba(129, 139, 152, 0.12); border-radius: 6px;">SenseVoice</code><span>&nbsp;</span>模型对语种、情感和事件打标。</p><div class="highlight highlight-source-shell notranslate position-relative overflow-auto" dir="auto" style="box-sizing: border-box; position: relative !important; overflow: auto !important; margin-bottom: 16px; display: flex; justify-content: space-between; background-color: rgb(246, 248, 250);"><pre style="box-sizing: border-box; font-family: ui-monospace, SFMono-Regular, &quot;SF Mono&quot;, Menlo, Consolas, &quot;Liberation Mono&quot;, monospace; font-size: 13.6px; margin-top: 0px; margin-bottom: 0px; overflow-wrap: normal; padding: 16px; overflow: auto; line-height: 1.45; color: rgb(31, 35, 40); background-color: rgb(246, 248, 250); border-radius: 6px; word-break: normal; min-height: 52px;"><span class="pl-c" style="box-sizing: border-box; color: rgb(89, 99, 110);"><span class="pl-c" style="box-sizing: border-box; color: rgb(89, 99, 110);">#</span> generate train.jsonl and val.jsonl from wav.scp and text.txt</span>
sensevoice2jsonl \
++scp_file_list=<span class="pl-s" style="box-sizing: border-box; color: rgb(10, 48, 105);"><span class="pl-pds" style="box-sizing: border-box; color: rgb(10, 48, 105);">'</span>["../../../data/list/train_wav.scp", "../../../data/list/train_text.txt"]<span class="pl-pds" style="box-sizing: border-box; color: rgb(10, 48, 105);">'</span></span> \
++data_type_list=<span class="pl-s" style="box-sizing: border-box; color: rgb(10, 48, 105);"><span class="pl-pds" style="box-sizing: border-box; color: rgb(10, 48, 105);">'</span>["source", "target"]<span class="pl-pds" style="box-sizing: border-box; color: rgb(10, 48, 105);">'</span></span> \
++jsonl_file_out=<span class="pl-s" style="box-sizing: border-box; color: rgb(10, 48, 105);"><span class="pl-pds" style="box-sizing: border-box; color: rgb(10, 48, 105);">"</span>../../../data/list/train.jsonl<span class="pl-pds" style="box-sizing: border-box; color: rgb(10, 48, 105);">"</span></span> \
++model_dir=<span class="pl-s" style="box-sizing: border-box; color: rgb(10, 48, 105);"><span class="pl-pds" style="box-sizing: border-box; color: rgb(10, 48, 105);">'</span>iic/SenseVoiceSmall<span class="pl-pds" style="box-sizing: border-box; color: rgb(10, 48, 105);">'</span></span></pre><div class="zeroclipboard-container" style="box-sizing: border-box; display: block; animation: auto ease 0s 1 normal none running none;"><clipboard-copy aria-label="Copy" class="ClipboardButton btn btn-invisible js-clipboard-copy m-2 p-0 d-flex flex-justify-center flex-items-center" data-copy-feedback="Copied!" data-tooltip-direction="w" value="# generate train.jsonl and val.jsonl from wav.scp and text.txt
sensevoice2jsonl \
++scp_file_list='[&quot;../../../data/list/train_wav.scp&quot;, &quot;../../../data/list/train_text.txt&quot;]' \
++data_type_list='[&quot;source&quot;, &quot;target&quot;]' \
++jsonl_file_out=&quot;../../../data/list/train.jsonl&quot; \
++model_dir='iic/SenseVoiceSmall'" tabindex="0" role="button" style="box-sizing: border-box; position: relative; display: flex !important; padding: 0px !important; font-size: 14px; font-weight: 500; line-height: 20px; white-space: nowrap; vertical-align: middle; cursor: pointer; user-select: none; border: 0px; border-radius: 6px; appearance: none; color: rgb(9, 105, 218); background-color: transparent; box-shadow: none; transition: color 80ms cubic-bezier(0.33, 1, 0.68, 1), background-color, box-shadow, border-color; justify-content: center !important; align-items: center !important; margin: 8px !important; width: 28px; height: 28px;"><svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-copy js-clipboard-copy-icon"><path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path><path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path></svg></clipboard-copy></div></div></details>

### 启动训练



注意修改 `finetune.sh` 中 `train_tool` 为你前面安装 FunASR 路径中 `funasr/bin/train_ds.py` 绝对路径

```
bash finetune.sh
```

### 训练日志解读

---

```bash
[2025-06-26 15:26:12,818][root][INFO] - 
train, 						#当前训练日志，有train或val
rank: 0,					#在分布式训练中，这代表当前是**第0号GPU**（或进程）打印的日志。
epoch: 0/50, 				#当前处于第几个训练周期（epoch）。
data_slice: 0/1, step_in_slice: 1/76, step_in_epoch: 1, total step: 1, (loss_avg_rank: 27.328), (loss_avg_slice: 27.328), (ppl_avg_slice: 7.387e+11), (acc_avg_slice: 0.000), (lr: 1.600e-06), [('loss_ctc', 26.544), ('loss_rich', 0.784), ('loss', 27.328), ('acc_rich', 0.83)], {'data_load': '0.307', 'forward_time': '0.293', 'backward_time': '0.060', 'optim_time': '0.080', 'total_time': '0.741'}, GPU, memory: usage: 2.660 GB, peak: 4.412 GB, cache: 4.773 GB, cache_peak: 4.773 GB
```

当然，我们来逐一解析这条训练日志，它像一个仪表盘，展示了模型在某一训练时刻的完整状态。

```
[2025-06-26 15:26:12,818][root][INFO] - train, rank: 0, epoch: 0/50, data_slice: 0/1, step_in_slice: 1/76, step_in_epoch: 1, total step: 1, (loss_avg_rank: 27.328), (loss_avg_slice: 27.328), (ppl_avg_slice: 7.387e+11), (acc_avg_slice: 0.000), (lr: 1.600e-06), [('loss_ctc', 26.544), ('loss_rich', 0.784), ('loss', 27.328), ('acc_rich', 0.83)], {'data_load': '0.307', 'forward_time': '0.293', 'backward_time': '0.060', 'optim_time': '0.080', 'total_time': '0.741'}, GPU, memory: usage: 2.660 GB, peak: 4.412 GB, cache: 4.773 GB, cache_peak: 4.773 GB
```

### 1. 训练进度信息

*   `train`: 表明这是一条**训练**过程中的日志，与之对应的是 `validation` 或 `test`。
*   `rank: 0`: 在分布式训练中，这代表当前是**第0号GPU**（或进程）打印的日志。
*   `epoch: 0/50`: 当前处于第 **0** 个训练周期（epoch），总共计划训练 **50** 个周期。
*   `data_slice: 0/1`: 数据分片信息。这里表示数据没有被切分，当前是第0个分片，总共1个。
*   `step_in_slice: 1/76`: 在当前数据分片中，这是第 **1** 步（step/batch），该分片总共有 **76** 步。
*   `step_in_epoch: 1`: 在当前训练周期（epoch 0）中，这是第 **1** 步。
*   `total step: 1`: 从训练开始以来，总的步数。

### 2. 核心性能指标（平均值）

这些是**累积平均值**，能反映训练过程的整体趋势。

*   `(loss_avg_rank: 27.328)`: 当前 rank (GPU 0) 的平均损失值。
*   `(loss_avg_slice: 27.328)`: 当前数据分片的平均损失值。
*   `(ppl_avg_slice: 7.387e+11)`: Perplexity (困惑度) 的平均值。这是衡量语言模型性能的指标，越低越好。一个极高的初始值是正常的。
*   `(acc_avg_slice: 0.000)`: 准确率（Accuracy）的平均值。这里可能是指CTC解码后的词准确率，初始为0很正常。
*   `(lr: 1.600e-06)`: 当前的学习率（Learning Rate）是 **0.0000016**。这个值会根据你设置的学习率调度器（scheduler）动态变化。

### 3. 当前步骤的详细损失 (最关键的部分)

这部分显示了**当前这一个step**计算出的具体损失值，直接反映了模型在该批次数据上的表现。

*   `[('loss_ctc', 26.544), ('loss_rich', 0.784), ('loss', 27.328), ('acc_rich', 0.83)]`:
    *   `('loss_ctc', 26.544)`: **CTC损失**，值为 **26.544**。这是核心的语音识别任务损失，衡量模型生成正确文本序列的能力。
    *   `('loss_rich', 0.784)`: **Rich-Token损失**，值为 **0.784**。这是交叉熵损失，衡量模型理解和预测我们之前分析的 "Queries" (Language, Style, Emotion) 的能力。
    *   `('loss', 27.328)`: **总损失**，值为 **27.328**。它就是 `loss_ctc` 和 `loss_rich` 的和 (`26.544 + 0.784 = 27.328`)。优化器会根据这个总损失来更新模型参数。
    *   `('acc_rich', 0.83)`: **Rich-Token的准确率**，为 **83%**。这表明模型在预测那几个多任务指令时，有83%的准确率，表现不错。

### 4. 耗时与资源监控

*   `{'data_load': '0.307', 'forward_time': '0.293', 'backward_time': '0.060', 'optim_time': '0.080', 'total_time': '0.741'}`:
    *   `data_load`: 加载这一批次数据耗时 **0.307秒**。
    *   `forward_time`: 前向传播（模型计算）耗时 **0.293秒**。
    *   `backward_time`: 反向传播（计算梯度）耗时 **0.060秒**。
    *   `optim_time`: 优化器更新参数耗时 **0.080秒**。
    *   `total_time`: 处理这一个step的总耗时 **0.741秒**。
*   `GPU, memory: usage: 2.660 GB, peak: 4.412 GB, cache: 4.773 GB, cache_peak: 4.773 GB`:
    *   `usage`: 当前GPU显存实时使用量 **2.660 GB**。
    *   `peak`: 训练开始到现在的显存使用峰值 **4.412 GB**。
    *   `cache`: PyTorch缓存的显存大小 **4.773 GB**。
    *   `cache_peak`: PyTorch缓存的峰值大小 **4.773 GB**。

**总结**: 这条日志告诉我们，在训练刚开始的第一个批次，模型在GPU 0上，总损失是27.328（主要来自CTC损失），学习率很低，处理这个批次花了0.741秒，显存峰值用量达到了4.4GB。这是一个非常典型的、信息量丰富的训练初期日志。

### 导出 & 推理

训练完成后，可导出为 ONNX 或 libtorch 格式，并结合 FunASR 的 `generate` 接口调用推理：

```
python复制编辑from funasr import AutoModel
model = AutoModel(model="./outputs/best_model", trust_remote_code=True, device="cuda:0")
res = model.generate(input="test.wav", language="auto", use_itn=False)
print(res)
```

同时支持导出：

```
python复制编辑from funasr_onnx import SenseVoiceSmall
model = SenseVoiceSmall("./outputs/best_model", batch_size=1, quantize=True)
print(model(["test.wav"]))
```