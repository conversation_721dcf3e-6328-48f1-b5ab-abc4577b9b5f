
import base64
import os
from fastapi import FastAPI, File, UploadFile, HTTPException, Body
from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess
from pydantic import BaseModel
import uvicorn
import time
from contextlib import asynccontextmanager

# 初始化应用
app = FastAPI()

# 全局模型变量
model = None

def load_model():
    global model
    if model is None:
        model = AutoModel(
            model="./iic/SenseVoiceSmall",
            vad_model="fsmn-vad",
            vad_kwargs={"max_single_segment_time": 30000},
            device="cuda:0",
        )

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 初始化模型
    load_model()
    yield
    # 可选：关闭/清理逻辑

app = FastAPI(lifespan=lifespan)



class ASRResponse(BaseModel):
    code: int
    message: str
    text: str | None = None


@app.post("/asr/file", response_model=ASRResponse)
async def asr_file(file: UploadFile = File(...)):
    try:
        if not file.filename.lower().endswith((".wav", ".mp3", ".flac", ".ogg")):
            raise HTTPException(status_code=400, detail="Unsupported audio format")
        s = time.time()
        tmp_file = f"tmp_{file.filename}"
        with open(tmp_file, "wb") as f:
            f.write(await file.read())
        res = model.generate(
            input=tmp_file,
            cache={},
            language="zh",
            use_itn=True,
            batch_size_s=60,
            merge_vad=True,
            merge_length_s=15
        )
        os.remove(tmp_file)
        # data = rich_transcription_postprocess(res[0])
        text = rich_transcription_postprocess(res[0]["text"])
        e = time.time()
        print(f"{(e-s)*1000:.2f}ms")
        print(text)
        return ASRResponse(code=0, message="ok", text=text)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health():
    return {"status": "healthy"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=34578)
