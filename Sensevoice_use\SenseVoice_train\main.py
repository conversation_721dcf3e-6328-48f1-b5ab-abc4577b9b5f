import json
import os
from pathlib import Path
import subprocess
import time
import uuid
import zipfile
from fastapi import BackgroundTasks, FastAPI, Form, HTTPException, UploadFile, File
from flask import render_template
from pydantic import BaseModel
from fastapi.responses import FileResponse, HTMLResponse, JSONResponse
from typing import List
from audio_processing import AudioProcessing
import uvicorn
from clearvoice import ClearVoice

app = FastAPI()

SPEECH_SEPARATION_WAV_PATH = "/home/<USER>/emotion2vec/audio-mark-tool/speech_separation_wav"#音频切分后保存路径
TEMP_DIR = "/home/<USER>/emotion2vec/audio-mark-tool/uploads"#临时文件保存路径
MARKED_RAW_JSON = "/home/<USER>/emotion2vec/audio-mark-tool/marked_json/marked_raw.json"#已标记数据Json保存路径
OUTPUTS_DIRECTORY = "/home/<USER>/emotion2vec/audio-mark-tool/outputs"#标记后完整上下文音频输出文件保存路径
OUTPUTS_TIME_LOG="/home/<USER>/emotion2vec/audio-mark-tool/log/outputs_time_log.jsonl"#输出时间Log
MARKED_WAV_PATH="/home/<USER>/emotion2vec/audio-mark-tool/marked_wav"#已标记的音频

audio_processor = AudioProcessing()

myClearVoice = ClearVoice(task='speech_separation', model_names=['MossFormer2_SS_16K'])

class AudioSegment(BaseModel):
    start: float
    end: float
    speaker: int
    transcription: str
    audioUrl: str
    sentiment: str


from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有的前端请求
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 直接返回index.html页面
@app.get("/", response_class=HTMLResponse)
async def index():
    # 返回Vue转换后的HTML文件
    index_file_path = Path("templates/App.vue")
    with open(index_file_path, "r") as f:
        return HTMLResponse(content=f.read())


@app.post("/upload_audio")
async def upload_audio(oracle_num=Form(...), audio: UploadFile = File(...)):
    """
    处理音频文件的上传并返回模拟的音频片段信息。
    """
    uuid1 = str(uuid.uuid4())
    upload_file_path = Path(f"{TEMP_DIR}/{uuid1}.wav")
    with open(upload_file_path, "wb") as f:
        f.write(await audio.read())

    audio_segments = audio_processor.process(
        str(upload_file_path),uuid_str=uuid1, oracle_num=int(oracle_num)
    )
    with open(OUTPUTS_TIME_LOG
        , "a"
    ) as f:
        json.dump({"uuid": uuid1, "time": time.time()},f)
        f.write("\n")
    os.remove(upload_file_path)

    # 生成独立的 JSON 数据
    #individual_jsons = generate_individual_conversations(audio_segments)
    # 返回音频片段信息
    return JSONResponse(
        content={"audioSegments": [seg.dict() for seg in audio_segments]}
    )


@app.get("/outputs/{file_path:path}")
async def get_audio_file(file_path: str):
    #OUTPUTS_DIRECTORY = "/home/<USER>/emotion2vec/audio-mark-tool/outputs"
    full_file_path = os.path.join(OUTPUTS_DIRECTORY, file_path)
    if not os.path.isfile(full_file_path):
        raise HTTPException(status_code=404, detail="File not found")

    # 返回文件响应
    return FileResponse(full_file_path)


def merge_audio(segments: List[AudioSegment], wav_name: str, output_dir: str) -> str:
    """
    合并音频片段，使用ffmpeg，通过文件列表传递音频路径，避免使用管道。
    :param segments: 音频片段列表
    :param wav_name: 合并音频的文件名（无扩展名）
    :param output_dir: 输出目录
    :return: 合并后的音频文件路径
    """
    # 使用绝对路径确保路径是正确的
    file_list_path = os.path.join(output_dir, f"{wav_name}_file_list.txt")

    with open(file_list_path, "w") as file_list:
        for segment in segments:
            absolute_path = os.path.abspath(segment.audioUrl)  # 使用绝对路径
            file_list.write(f"file '{absolute_path}'\n")  # 写入文件列表

    # 准备ffmpeg的输入命令
    merged_audio_path = os.path.join(output_dir, f"{wav_name}.wav")
    cmd = [
        "ffmpeg",
        "-f",
        "concat",
        "-safe",
        "0",
        "-i",
        file_list_path,
        "-c",
        "copy",
        merged_audio_path,
    ]

    # 执行命令合并音频
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

    # 获取并检查ffmpeg执行的输出
    stdout, stderr = process.communicate()

    # 如果有错误，抛出异常
    if process.returncode != 0:
        raise Exception(f"FFmpeg error: {stderr.decode()}")

    # 删除临时文件
    os.remove(file_list_path)

    # 返回合并后的音频文件路径
    return merged_audio_path


def generate_individual_conversations(segments):
    """
    根据每个音频片段生成独立的 JSON 数据，每个片段为一份数据，包含对话内容和对应情感。
    """
    result_jsons = []  # 用于存储每个音频片段生成的 JSON 数据
    segments_list = []
    transcription = ""
    uuid_str = str(uuid.uuid4())
    # 处理每个音频片段，生成独立的 JSON
    for i, segment in enumerate(segments):
        transcription += "\nHuman: " + segment.transcription
        segments_list.append(segment)
        audioUrl = merge_audio(
            segments_list,
            f"{uuid_str}_{i}",
            MARKED_WAV_PATH,
        )
        # 为每个片段生成独立的JSON
        conversation_data = {
                    "id": f"conversation_{uuid_str}",
                    "audioUrl": audioUrl,
                    "conversations": [
                        {
                            "from": "human",
                            "value": f"请根据以下对话内容判断整体情感是什么：{transcription}",
                        },
                        {
                            "from": "gpt",
                            "value": [{"value": segment.sentiment}],
                        }
                    ],
                    "system": "你是一个情感分析师，可以根据多轮对话内容精确判断情感。"    
                }

        result_jsons.append(conversation_data)

    return result_jsons


@app.get("/get_json_count")
async def submit_edits():

    with open(MARKED_RAW_JSON, "r") as f:
        data = f.read()
        if data == "":
            old_merged_segments = []
        else:
            old_merged_segments = json.loads(data)
            #print(old_merged_segments)

    return {"json_count": len(old_merged_segments)}


def clear_outputs():
   
    with open(OUTPUTS_TIME_LOG, "r",encoding="utf-8") as f:
        data = f.read()
        if data == "":
            files = []
        else:
            f.seek(0)
            files = [json.loads(line.strip()) for line in f]
    file_list = []  
    
    for entry in files:
        for dirname in os.listdir(OUTPUTS_DIRECTORY):
            if dirname == entry["uuid"] and time.time() - entry["time"] > 86400:
                for filename in os.listdir(os.path.join(OUTPUTS_DIRECTORY, dirname)):
                    file_path = os.path.join(
                        os.path.join(OUTPUTS_DIRECTORY, dirname), filename
                    )
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                        print(f"已删除文件：{file_path}")
                os.removedirs(os.path.join(OUTPUTS_DIRECTORY, dirname))
                print(f"已删除文件夹：{os.path.join(OUTPUTS_DIRECTORY, dirname)}")
                entry={}
                break
        if entry:
            file_list.append(entry)
    with open(OUTPUTS_TIME_LOG, "w",encoding="utf-8") as f:
            for entry in file_list:
                json.dump(entry, f)
                f.write("\n")
# @app.post("/submit_modified")
# async def submit_edits(segments: List[AudioSegment]):
#     """
#     处理用户提交的音频片段编辑信息和情感标注数据。
#     """
#     # 打印用户提交的音频片段和情感标注数据
#     # 调用函数处理

#     merged_segments = generate_individual_conversations(segments)
#     with open(MARKED_RAW_JSON, "r") as f:
#         data = f.read()
#         if data == "":
#             old_merged_segments = []
#         else:
#             old_merged_segments = json.loads(data)
#             #print(old_merged_segments)
#     for entry in old_merged_segments:
#         merged_segments.append(entry)
#     with open(MARKED_RAW_JSON, "w") as f:
#         json.dump(merged_segments, f, ensure_ascii=False, indent=4)
#     # print(json.dumps(result_json, ensure_ascii=False, indent=4))
#     clear_outputs()
#     return {"message": "修改已提交成功"}

@app.post("/submit_modified")
async def submit_edits(segments: List[AudioSegment]):
    """
    处理用户提交的音频片段编辑信息和情感标注数据。
    """
    try:
        # 生成独立的 JSON 数据
        merged_segments = generate_individual_conversations(segments)

        # 读取已有的 JSON 数据
        try:
            with open(MARKED_RAW_JSON, "r", encoding="utf-8") as f:
                data = f.read()
                if data.strip() == "":
                    old_merged_segments = []
                else:
                    old_merged_segments = json.loads(data)
        except FileNotFoundError:
            old_merged_segments = []
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid JSON format in MARKED_RAW_JSON")

        # 合并新旧数据
        merged_segments.extend(old_merged_segments)

        # 写入合并后的数据到 JSON 文件
        try:
            with open(MARKED_RAW_JSON, "w", encoding="utf-8") as f:
                json.dump(merged_segments, f, ensure_ascii=False, indent=4)
        except IOError:
            raise HTTPException(status_code=500, detail="Failed to write to MARKED_RAW_JSON")

        # 清理输出文件
        clear_outputs()

        return {"message": "修改已提交成功"}
    except Exception as e:
        return {"message": f"处理失败: {str(e)}", "details": str(e)}


@app.get("/get_json_file")
async def get_json_file(timestamp):
    return FileResponse(MARKED_RAW_JSON)
   
@app.post("/clear_json")
async def clear_json():
    with open(MARKED_RAW_JSON, 'w') as file:
        file.truncate(0)  # 或者直接：file.write('')
    return {"message": "JSON文件已清空"}
@app.post("/speech_separation")
async def speech_separation(background_tasks: BackgroundTasks,file: UploadFile = File(...)):
   
    try:
        
        uuid1 = str(uuid.uuid1())   
        upload_file_path = f"{TEMP_DIR}/{uuid1}.wav"
        with open(upload_file_path, "wb") as f:
            f.write(await file.read())
        
        myClearVoice(input_path=upload_file_path, online_write=True, output_path=SPEECH_SEPARATION_WAV_PATH)
            # 获取分离后的音频文件路径（假设生成了多个文件）
        OUTPUTS_DIR = f"{SPEECH_SEPARATION_WAV_PATH}/MossFormer2_SS_16K"
        # 获取所有分离的音频文件
        audio_files = [Path(OUTPUTS_DIR, f) for f in os.listdir(OUTPUTS_DIR) if f.endswith('.wav')]

        # 创建一个临时压缩文件
        zip_file_path = Path(TEMP_DIR, f"{uuid1}.zip")
        with zipfile.ZipFile(zip_file_path, 'w') as zipf:
            for audio_file in audio_files:
                zipf.write(audio_file, arcname=audio_file.name)
        # 返回压缩文件
        response = FileResponse(zip_file_path, media_type='application/zip', filename=f"{file.filename}.zip")
        # 在文件发送后删除临时的压缩文件
        def remove_file():
            os.remove(zip_file_path)
            for wav_file in audio_files:
                os.remove(wav_file)
            os.remove(upload_file_path)
            print(f"临时文件 {zip_file_path} 已删除")
        background_tasks.add_task(remove_file)
        # 返回压缩文件
        return response
    except Exception as e:
        print(f"Error during processing: {e}")
        return {"message": "Processing failed", "details": str(e)}


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=6001)
