import time
import uuid
from flask import (
    Flask,
    json,
    jsonify,
    make_response,
    request,
    send_file,
    send_from_directory,
    render_template,
)
import os

app = Flask(__name__)


JSON_PATH = "/home/<USER>/emotion2vec/data/json/session1213/session.jsonl"
SENSEVOICE_MARK_JSON_PATH = (
    "/home/<USER>/emotion2vec/data/json/session1213/sensevoice_marked.jsonl"
)
OTHER_JSON_PATH = "/home/<USER>/emotion2vec/data/json/session1213/other_marked.jsonl"
SEMANTIC_EMO_MARK_JSON_PATH = (
    "/home/<USER>/emotion2vec/data/json/session1213/sentantic_marked.jsonl"
)
modified_files = set()
import os

print("Current working directory:", os.getcwd())
current_file = None  # 当前处理的文件
total_len = 0
current_line_index = 0  # 当前行索引


@app.route("/vvue")
def index():
    # 检查用户是否已有分配的 user_id
    user_id = request.cookies.get("user_id")
    if not user_id:
        # 如果没有，生成一个新的 UUID
        user_id = str(uuid.uuid4())
    response = make_response(render_template("index_voice.vue"))
    # 将 user_id 写入 Cookie，设置有效期为 30 天
    response.set_cookie("user_id", user_id, max_age=60 * 60 * 24 * 30)
    return response


from datetime import datetime, timedelta

import threading

# 用于记录每条数据的分配状态
data_status = {}  # {key: {'user': None, 'timestamp': None}}

lock = threading.Lock()  # 确保状态更新线程安全
TIMEOUT = 1200  # 数据超时时间（秒）


@app.route("/get_file", methods=["GET"])
def get_file():
    global current_file, total_len
    user_id = request.args.get("user_id")  # 从请求中获取用户 ID
    print(user_id)
    if current_file is None:
        current_file = JSON_PATH

    with open(current_file, "r", encoding="utf-8") as f:
        lines = f.readlines()
    total_len = len(lines)
    for line in lines:
        json_data = json.loads(line)
        key = json_data["source"]  # 假设每条数据有唯一 key

        with lock:
            # 检查数据是否已分配，若未分配或超时则分配给当前用户
            if (
                key not in data_status
                or data_status[key]["user"] is None
                or (datetime.now() - data_status[key]["timestamp"]).seconds > TIMEOUT
            ):
                data_status[key] = {"user": user_id, "timestamp": datetime.now()}
                return jsonify(json_data=json_data)
            elif user_id == data_status[key]["user"]:
                return jsonify(json_data=json_data)
    return jsonify(message="没有更多文件可修改。")


@app.route("/modify_file", methods=["POST"])
def modify_file():

    data = request.json
    user_id = data["user_id"]
    modified_audio_info = data["json_content"]
    json_object = json.loads(modified_audio_info)
    key = json_object["source"]

    with lock:
        # 检查数据是否属于该用户，且未超时
        if (
            key not in data_status
            or data_status[key]["user"] != user_id
            or (datetime.now() - data_status[key]["timestamp"]).seconds > TIMEOUT
        ):
            return jsonify(
                message="提交失败，数据无效或已超时，请刷新获取新数据。", code=500
            )

    # 保存标注逻辑
    # semantic_emo_target = json_object.get('semantic_emo_target', None)
    target_len = json_object["target_len"]
    if target_len != 0:
        if json_object["emo_target"] == "<|OTHER|>":
            with open(OTHER_JSON_PATH, "a", encoding="utf-8") as f:
                f.write(json.dumps(json_object, ensure_ascii=False) + "\n")
        else:
            with open(SENSEVOICE_MARK_JSON_PATH, "a", encoding="utf-8") as f:

                f.write(json.dumps(json_object, ensure_ascii=False) + "\n")

    # 从未标记 JSONL 文件中删除该条数据
    remaining_lines = []
    with open(JSON_PATH, "r", encoding="utf-8") as f:
        for line in f:
            audio_info = json.loads(line.strip())
            if str(audio_info["source"]) != json_object["source"]:
                remaining_lines.append(line)

    with open(JSON_PATH, "w", encoding="utf-8") as f:
        f.writelines(remaining_lines)
    with lock:
        data_status[key]["user"] = None  # 释放数据
    return jsonify(message="文件修改成功。", code=200)


def cleanup_data_status():
    while True:
        with lock:
            keys_to_remove = []
            for key, info in data_status.items():
                if (
                    info["user"] is None
                    or (datetime.now() - info["timestamp"]).seconds > TIMEOUT
                ):
                    keys_to_remove.append(key)
            for key in keys_to_remove:
                del data_status[key]
        time.sleep(600)  # 每 10 分钟清理一次


# 启动后台线程
threading.Thread(target=cleanup_data_status, daemon=True).start()


@app.route("/get_total", methods=["GET"])
def get_total():
    global total_len
    return jsonify(total_len=total_len)


@app.route("/get_completedFiles", methods=["GET"])
def get_completedFiles():
    with open(SENSEVOICE_MARK_JSON_PATH, "r", encoding="utf-8") as f:
        lines = f.readlines()
        total_len = len(lines)
    return jsonify(total_len=total_len)


@app.route("/audio/<path:filename>", methods=["GET"])
def serve_audio(filename):
    print(filename)
    full_path = "/" + filename
    return send_file(full_path)


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=6002)
