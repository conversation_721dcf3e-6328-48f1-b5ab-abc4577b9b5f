1.对象“对话输入”有一个属性“embedding",里面保存了"输入"文本的向量值（可以理解为tokens)，同时这个属性对应一个索引index名称是psyinput_embeddings，用于提高查询效率
2.要比较2个文本是否相似，通常用余弦相似度方法。neo4j里面有一个用于查询节点向量索引的函数，可以返回相似度最高的top k记录，你去查一查具体的使用情况
3.利用python编写一个函数query_similar(inputtext:str)，参数是任意输入文本，然后从数据库中的"对话输入"实体的"输入"属性对应的索引，找到相似度最高的前3个记录，然后打印出来
4.函数的参数inputtext文本是要先转换成向量，才能和对应的索引作比较的。服务器那边通过ollama部署了一个可以生成向量的模型(BGE-M3
)接口，可以通过http post请求并返回json格式的结果，接口地址：http://localhost:11434/api/embed
，请求json格式是
    {
        "model": "bge-m3:567m",
        "input": inputtext
    }


{
  "text": "<think>\n好的，我现在要判断用户的情绪，并输出对应的JSON数据。首先，我需要仔细阅读用户提供的任务说明和示例输出。\n\n任务要求判断用户的情绪类别：自然、开心、伤心、生气、害怕。然后按照指定的格式返回结果。\n\n接下来分析用户给出的输入内容：\n用户说：“我当时不在，他拿了我的盆去刷厕所，我很生气，当时很难理解，明明可以用微信跟我说一下，我借一下你的盆，我也不会说什么。”\n\n首先，用户提到“我很生气”，这直接表明了情绪类别是生气。接着，用户的描述中还提到了无法理解对方的行为，并且表达了希望沟通的愿望。这些内容进一步确认了用户的情绪状态。\n\n在分析过程中，我注意到用户没有使用过于强烈的情感词汇，只是明确表达了自己的情绪，所以可以判断为生气而不至于归类为其他情绪类别。\n\n因此，基于以上分析，判断用户的 emotion 是“生气”，然后按照示例格式生成相应的JSON数据。\n</think>\n\n```json\n{ \"emotion\": \"生气\" }\n```",
  "usage": {
    "prompt_tokens": 77,
    "prompt_unit_price": "0",
    "prompt_price_unit": "0",
    "prompt_price": "0",
    "completion_tokens": 217,
    "completion_unit_price": "0",
    "completion_price_unit": "0",
    "completion_price": "0",
    "total_tokens": 294,
    "total_price": "0",
    "currency": "USD",
    "latency": 1.756411156617105
  },
  "finish_reason": "stop"
}