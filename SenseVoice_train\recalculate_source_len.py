import json
import librosa
import os

try:
    from tqdm import tqdm
except ImportError:
    print("[!] 依赖 'tqdm' 未找到。请使用 'pip install tqdm' 命令安装。")
    exit(1)


def calculate_fbank_source_len(file_path):
    """
    通过计算音频的fbank特征帧数来获取source_len。
    这与您其他脚本中的计算方法保持一致。
    """
    # 首先检查文件是否存在，避免程序因文件找不到而中断
    if not os.path.exists(file_path):
        print(f" [!] 警告: 音频文件不存在 '{file_path}'。跳过此条目。")
        return None

    try:
        # 使用 librosa 加载音频文件, sr=None 会保持文件的原始采样率
        y, sr = librosa.load(file_path, sr=None)

        # fbank 特征参数 (与您项目中的其他脚本保持一致)
        n_fft = 2048
        hop_length = 512
        n_mels = 128

        # 计算 fbank 特征 (mel 频谱图)
        fbank = librosa.feature.melspectrogram(
            y=y, sr=sr, n_fft=n_fft, hop_length=hop_length, n_mels=n_mels
        )

        # 新的 source_len 是 fbank 表示中的帧数
        source_len = fbank.shape[1]
        return source_len
    except Exception as e:
        print(f" [!] 处理文件时发生错误 {file_path}: {e}")
        return None


def process_jsonl(input_file, output_file, audio_base_path):
    """
    读取JSONL文件，重新计算source_len，并写入新文件。

    参数:
        input_file (str): 源JSONL文件路径。
        output_file (str): 用于保存更新后数据的JSONL文件路径。
        audio_base_path (str): 音频数据存放的本地根目录。
    """

    # 这是我们期望在文件中找到并替换的路径前缀
    linux_base_path = "/home/<USER>/emotion2vec/data"
    updated_records = []

    print(f"[*] 读取文件: {input_file}")
    print(f"[*] 音频文件根目录: {audio_base_path}")
    print(f"[*] 输出文件: {output_file}")

    try:
        with open(input_file, "r", encoding="utf-8") as f:
            lines = f.readlines()

        for line in tqdm(lines, desc="处理记录中"):
            line = line.strip()
            if not line:
                continue

            try:
                data = json.loads(line)
                original_source_path = data.get("source")

                if not original_source_path:
                    # 如果记录中没有 'source' 字段，我们无法处理，直接保留原样
                    updated_records.append(data)
                    continue

                # --- 路径转换 ---
                # 检查文件中的路径是否以预期的Linux前缀开头
                if original_source_path.startswith(linux_base_path):
                    # 剥离Linux前缀和开头的斜杠
                    relative_path = original_source_path[len(linux_base_path) :].lstrip(
                        "/"
                    )
                    # 与用户提供的本地根目录拼接成完整路径
                    local_audio_path = os.path.join(audio_base_path, relative_path)
                else:
                    # 如果路径不匹配，打印警告并假设它是一个可以直接拼接的相对路径
                    print(
                        f"\n [!] 警告: 路径 '{original_source_path}' 与预期的前缀 '{linux_base_path}' 不匹配。将尝试作为相对路径处理。"
                    )
                    local_audio_path = os.path.join(
                        audio_base_path, os.path.basename(original_source_path)
                    )

                # --- 重新计算 ---
                new_source_len = calculate_fbank_source_len(local_audio_path)

                if new_source_len is not None:
                    # 使用新的source_len更新记录
                    data["source_len"] = new_source_len

                updated_records.append(data)

            except json.JSONDecodeError:
                print(f"\n [!] 跳过无效的JSON行: {line}")
                continue

        # 将更新后的所有记录写入新文件
        with open(output_file, "w", encoding="utf-8") as f:
            for record in updated_records:
                f.write(json.dumps(record, ensure_ascii=False) + "\n")

        print(f"\n[+] 成功！处理完成。更新后的数据已保存至 {output_file}")

    except FileNotFoundError:
        print(f"\n[!] 错误: 输入文件未找到 '{input_file}'")
    except Exception as e:
        print(f"\n[!] 发生未知错误: {e}")


if __name__ == "__main__":
    # --- 用户配置区域 ---
    # 您可以在这里直接修改脚本的输入、输出和音频路径，
    # 而无需通过命令行传递参数。

    # 1. 源JSONL文件路径
    # 例如: "data/train.jsonl"
    INPUT_FILE = "/home/<USER>/emotion2vec/data/json/session_repeat_500/sensevoice_marked_from_db.jsonl"

    # 2. 输出JSONL文件路径
    # 处理后的数据将保存到这里。
    # 例如: "data/train_updated.jsonl"
    OUTPUT_FILE = "/home/<USER>/emotion2vec/data/json/session_repeat_500/sensevoice_marked_from_db1.jsonl"

    # 3. 音频数据根目录
    # 这是存放您所有音频文件的根文件夹。
    # 例如: 如果jsonl中的路径是 '/home/<USER>/data/audio/1.wav'
    # 且您的音频文件实际在 'D:\\my_data\\audio\\1.wav'
    # 那么这里应该设置为 'D:\\my_data'
    AUDIO_BASE_PATH = "/home/<USER>/emotion2vec/data/mp3"

    # --- 配置结束 ---

    process_jsonl(INPUT_FILE, OUTPUT_FILE, AUDIO_BASE_PATH)