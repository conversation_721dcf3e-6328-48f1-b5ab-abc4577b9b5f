import sqlite3
import time
import uuid
from flask import Flask, json, jsonify, make_response, request, send_file, render_template
import os
from datetime import datetime, timedelta
import threading
import queue

# --- 配置 ---
DATABASE_PATH = '/home/<USER>/emotion2vec/data/json/session_test_500/tasks.db'
# 用于一次性数据导入的原始JSONL文件路径
INITIAL_DATA_JSON_PATH = "/home/<USER>/emotion2vec/data/json/session_test_500/session.jsonl"
# 以下路径现在是用于数据导出，而不是作为主要数据存储
SENSEVOICE_MARK_JSON_PATH = (
    "/home/<USER>/emotion2vec/data/json/session_test_500/sensevoice_marked_from_db.jsonl"
)
OTHER_JSON_PATH = "/home/<USER>/emotion2vec/data/json/session_test_500/other_marked_from_db.jsonl"
SEMANTIC_EMO_MARK_JSON_PATH = (
    "/home/<USER>/emotion2vec/data/json/session_test_500/sentantic_marked_from_db.jsonl"
)

TIMEOUT = 1200  # 任务锁定超时时间（秒），20分钟
REAL_TIME_EXPORT = True  # 启用实时导出功能

# --- 异步导出配置 ---
export_queue = queue.Queue()

app = Flask(__name__)

# --- 数据库设置和初始化 ---

def get_db_connection():
    """创建数据库连接。"""
    conn = sqlite3.connect(DATABASE_PATH, timeout=5, check_same_thread=False)
    conn.row_factory = sqlite3.Row  # 允许通过列名访问数据
    return conn

def init_db():
    """初始化数据库，创建表，并从JSONL导入数据。"""
    if os.path.exists(DATABASE_PATH):
        conn_check = get_db_connection()
        try:
            count = conn_check.execute('SELECT COUNT(id) FROM tasks').fetchone()[0]
            if count > 0:
                print("数据库已存在且包含数据，跳过初始化。")
                conn_check.close()
                return
        except sqlite3.OperationalError:
            # 表可能不存在，继续执行初始化
            pass
        conn_check.close()
    
    print("正在初始化数据库...")
    conn = get_db_connection()
    cursor = conn.cursor()

    # 创建表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            source TEXT NOT NULL UNIQUE,
            target TEXT,
            text_language TEXT,
            emo_target TEXT,
            event_target TEXT,
            source_len INTEGER,
            target_len INTEGER,
            with_or_wo_itn TEXT,
            status TEXT NOT NULL,
            locked_by TEXT,
            locked_at DATETIME,
            modified_at DATETIME,
            semantic_emo_target TEXT
        )
    ''')

    # 如果存在，则从JSONL文件导入数据
    if not os.path.exists(INITIAL_DATA_JSON_PATH):
        print(f"警告: 在 {INITIAL_DATA_JSON_PATH} 未找到初始数据文件，数据库将为空。")
        conn.commit()
        conn.close()
        return

    print(f"正在从 {INITIAL_DATA_JSON_PATH} 导入数据...")
    task_count = 0
    with open(INITIAL_DATA_JSON_PATH, 'r', encoding='utf-8') as f:
        for line in f:
            try:
                data = json.loads(line)
                cursor.execute('''
                    INSERT INTO tasks (source, target, text_language, emo_target, event_target, source_len, target_len, with_or_wo_itn, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending')
                ''', (
                    data.get('source'), data.get('target'), data.get('text_language'),
                    data.get('emo_target'), data.get('event_target'), data.get('source_len'),
                    data.get('target_len'), data.get('with_or_wo_itn')
                ))
                task_count += 1
            except json.JSONDecodeError:
                print(f"跳过无效的JSON行: {line.strip()}")
            except sqlite3.IntegrityError:
                print(f"跳过重复的数据源: {data.get('source')}")

    conn.commit()
    conn.close()
    print(f"数据库初始化成功，共导入 {task_count} 条任务。")

# --- 异步导出工作线程 ---
def export_worker():
    """从队列中获取任务并将其导出到文件。"""
    print("异步导出工作线程已启动。")
    conn = None
    while True:
        try:
            # 可以处理两种队列项：完整任务字典或任务ID
            item = export_queue.get()
            if item is None:  # 用于停止工作线程的信号
                break
            
            # 检查是否只收到了任务ID而不是完整的任务字典
            if isinstance(item, int) or isinstance(item, str) and item.isdigit():
                # 如果只收到ID，需要创建自己的数据库连接来获取任务
                try:
                    if conn is None:
                        conn = get_db_connection()
                    task = conn.execute('SELECT * FROM tasks WHERE id = ?', (item,)).fetchone()
                    if task:
                        task_dict = dict(task)
                        # 使用现有的导出函数处理单个任务
                        export_task_to_jsonl(task_dict)
                    else:
                        print(f"导出工作线程无法找到ID为{item}的任务")
                except sqlite3.Error as e:
                    print(f"导出工作线程数据库错误: {e}")
                    # 如果出现数据库错误，重新建立连接
                    if conn:
                        conn.close()
                    conn = get_db_connection()
            else:
                # 原有方式处理完整任务字典
                export_task_to_jsonl(item)

            export_queue.task_done()
        except Exception as e:
            print(f"异步导出工作线程出现错误: {e}")
        
        # 如果队列为空，考虑关闭数据库连接以释放资源
        if export_queue.empty() and conn is not None:
            conn.close()
            conn = None

# --- 实时导出功能 ---

def export_task_to_jsonl(task_dict):
    """实时导出单个任务到相应的JSONL文件，根据指定格式"""
    try:
        # 如果target为空，跳过导出
        if not task_dict.get('target', '').strip():
            print(f"ID为{task_dict.get('id')}的任务文本为空，跳过导出")
            return True
            
        status = task_dict.get('status')
        source_path = task_dict.get('source', '')
        # 1. 根据要求修改key的格式
        key = f"mark_{os.path.basename(source_path)}" if source_path else ''
        
        # 2. 提取 semantic_emo_target 单独处理
        semantic_emo_target = task_dict.get('semantic_emo_target')

        # 构造基础的导出数据，不包含 semantic_emo_target
        base_export_data = {
            "emo_target": task_dict.get('emo_target', ''),
            "event_target": task_dict.get('event_target', ''),
            "key": key,
            "source": task_dict.get('source', ''),
            "source_len": task_dict.get('source_len', 0),
            "target": task_dict.get('target', ''),
            "target_len": task_dict.get('target_len', 0),
            "text_language": task_dict.get('text_language', ''),
            "with_or_wo_itn": task_dict.get('with_or_wo_itn')
        }
        
        if status == 'other':
            # 对于 'other' 任务，我们仍然包含所有信息
            other_export_data = base_export_data.copy()
            if semantic_emo_target:
                other_export_data["semantic_emo_target"] = semantic_emo_target
            
            with open(OTHER_JSON_PATH, 'a', encoding='utf-8') as f:
                f.write(json.dumps(other_export_data, ensure_ascii=False) + '\n')
            print(f"已实时导出ID为{task_dict.get('id')}的other任务")
        
        elif status == 'completed':
            # 1. 导出到 SENSEVOICE 文件（不含 semantic_emo_target）
            with open(SENSEVOICE_MARK_JSON_PATH, 'a', encoding='utf-8') as f_sv:
                f_sv.write(json.dumps(base_export_data, ensure_ascii=False) + '\n')
            
            # 2. 如果存在，单独导出到 SEMANTIC 文件
            if semantic_emo_target:
                semantic_data = {
                    "sentence": base_export_data.get('target'),
                    "semantic_emo_target": semantic_emo_target,
                }
                with open(SEMANTIC_EMO_MARK_JSON_PATH, 'a', encoding='utf-8') as f_sem:
                    f_sem.write(json.dumps(semantic_data, ensure_ascii=False) + '\n')
                    
            print(f"已实时导出ID为{task_dict.get('id')}的completed任务")
        
        return True
    except Exception as e:
        print(f"实时导出任务失败: {e}")
        return False

# --- Flask 路由 ---

@app.route("/vvue")
def index():
    """提供主页面并通过cookie分配用户ID。"""
    user_id = request.cookies.get("user_id")
    if not user_id:
        user_id = str(uuid.uuid4())
    # 确保您的模板文件名正确
    response = make_response(render_template("index_emo_1.vue"))
    response.set_cookie("user_id", user_id, max_age=60 * 60 * 24 * 30)
    return response


@app.route("/get_file", methods=["GET"])
def get_file():
    """
    为用户获取一个任务。
    优先返回已被该用户锁定的任务。
    如果没有，则以原子方式分配一个新的'pending'状态的任务。
    此操作现在对并发是安全的。
    """
    user_id = request.args.get("user_id")
    if not user_id:
        return jsonify(message="需要提供用户ID。", code=400), 400

    conn = None
    try:
        conn = get_db_connection()
        
        # 1. 检查用户是否已有锁定的任务（例如页面刷新）
        task = conn.execute(
            'SELECT * FROM tasks WHERE locked_by = ? AND status = ?', (user_id, 'locked')
        ).fetchone()

        if task:
            result = jsonify(json_data=dict(task))
            conn.close()
            return result

        # 2. 如果没有，尝试以原子方式查找并锁定一个新任务
        with conn:
            # 这个单一的UPDATE语句会原子性地找到第一个待处理任务并锁定它，
            # 从而防止了"先SELECT后UPDATE"的竞态条件。
            cursor = conn.execute(
                """UPDATE tasks
                   SET status = 'locked', locked_by = ?, locked_at = ?
                   WHERE id = (SELECT id FROM tasks WHERE status = 'pending' LIMIT 1)""",
                (user_id, datetime.now())
            )

            # 如果rowcount为0，表示子查询没有找到任何'pending'的任务
            if cursor.rowcount == 0:
                conn.close()
                return jsonify(message="所有数据已处理完毕，没有更多数据需要标注。")
        
        # 3. 任务已为我们锁定，现在找出它具体是哪个任务
        locked_task = conn.execute(
            'SELECT * FROM tasks WHERE locked_by = ? AND status = ?', (user_id, 'locked')
        ).fetchone()
        
        if locked_task:
            conn.close()
            return jsonify(json_data=dict(locked_task))
        else:
            # 这种情况不太可能发生，但作为保障
            conn.close()
            return jsonify(message="无法获取锁定的任务，请重试。", code=500), 500

    except sqlite3.OperationalError as e:
        # 捕获特定的"数据库锁定"错误，这是高并发下的常见问题
        if "database is locked" in str(e):
            print(f"数据库锁定 in /get_file: {e}")
            if conn:
                conn.close()
            return jsonify(message="服务器正忙，数据库锁定，请稍后重试。", code=503), 503
        else:
            print(f"数据库操作错误 in /get_file: {e}")
            if conn:
                conn.close()
            return jsonify(message="数据库操作错误，请稍后重试。", code=500), 500
    except sqlite3.Error as e:
        print(f"数据库错误 in /get_file: {e}")
        if conn:
            conn.close()
        return jsonify(message="数据库错误，请稍后重试。", code=500), 500
    except Exception as e:
        print(f"未预期的错误 in /get_file: {e}")
        if conn:
            conn.close()
        return jsonify(message="服务器发生错误，请稍后重试。", code=500), 500


@app.route("/modify_file", methods=["POST"])
def modify_file():
    """
    处理用户提交的已完成任务。
    原子性地更新数据库，并实时导出到JSONL文件。
    """
    data = request.json
    user_id = data.get("user_id")
    task_id = data.get("task_id") 
    modified_data = data.get("json_content")

    if not all([user_id, task_id, modified_data]):
        return jsonify(message="缺少 user_id, task_id, 或 json_content。", code=400), 400

    conn = get_db_connection()

    # 验证该任务当前是否由此用户锁定
    task = conn.execute(
        'SELECT * FROM tasks WHERE id = ? AND locked_by = ? AND status = ?',
        (task_id, user_id, 'locked')
    ).fetchone()

    if not task:
        conn.close()
        return jsonify(message="提交失败，任务无效或已被他人锁定，请刷新获取新数据。", code=403), 403
    
    # 检查是否超时
    if (datetime.now() - datetime.fromisoformat(task['locked_at'])).seconds > TIMEOUT:
        conn.close()
        return jsonify(message="提交失败，任务已超时，请刷新获取新数据。", code=408), 408

    # 用新数据更新任务
    new_status = 'completed'
    if modified_data.get("emo_target") == "<|OTHER|>" or modified_data.get("semantic_emo_target") == "<|OTHER|>":
        new_status = 'other'

    try:
        with conn:
            conn.execute(
                '''UPDATE tasks SET 
                   target = ?, 
                   target_len = ?,
                   semantic_emo_target = ?, 
                   emo_target = ?,
                   event_target = ?,
                   text_language = ?,
                   status = ?, 
                   modified_at = ?,
                   locked_by = NULL,
                   locked_at = NULL
                   WHERE id = ?''',
                (
                    modified_data.get('target'),
                    len(modified_data.get('target', '').replace(" ", "")),
                    modified_data.get('semantic_emo_target'),
                    modified_data.get('emo_target'),
                    modified_data.get('event_target'),
                    modified_data.get('text_language'),
                    new_status,
                    datetime.now(),
                    task_id
                )
            )
        
        # 改进的实时导出功能 - 只将任务ID放入队列，完全异步处理
        if REAL_TIME_EXPORT:
            # 不再查询数据库，直接将任务ID放入队列，让导出工作线程自己查询
            export_queue.put(task_id)
        
        return jsonify(message="文件修改成功。", code=200)
    except sqlite3.Error as e:
        print(f"数据库更新错误 in /modify_file: {e}")
        return jsonify(message="数据库更新时出错。", code=500), 500
    finally:
        conn.close()

def cleanup_timed_out_tasks():
    """定期检查锁定超时的任务并重置它们。"""
    while True:
        time.sleep(600) # 每10分钟检查一次
        try:
            print("正在清理超时任务...")
            conn = get_db_connection()
            timeout_threshold = datetime.now() - timedelta(seconds=TIMEOUT)
            
            with conn:
                tasks_to_reset = conn.execute(
                    "SELECT id FROM tasks WHERE status = 'locked' AND locked_at < ?",
                    (timeout_threshold,)
                ).fetchall()
                
                if tasks_to_reset:
                    task_ids = [(task['id'],) for task in tasks_to_reset]
                    print(f"发现 {len(task_ids)} 个超时任务需要重置。")
                    conn.executemany(
                        "UPDATE tasks SET status = 'pending', locked_by = NULL, locked_at = NULL WHERE id = ?",
                        task_ids
                    )
        except sqlite3.Error as e:
            print(f"清理任务时出错: {e}")
        finally:
            if conn:
                conn.close()

@app.route("/get_total", methods=["GET"])
def get_total():
    """获取待处理任务的总数。"""
    conn = get_db_connection()
    total = conn.execute("SELECT COUNT(id) FROM tasks WHERE status = 'pending'").fetchone()[0]
    conn.close()
    return jsonify(total_len=total)

@app.route("/get_completedFiles", methods=["GET"])
def get_completedFiles():
    """获取已完成任务的总数。"""
    conn = get_db_connection()
    total = conn.execute("SELECT COUNT(id) FROM tasks WHERE status IN ('completed', 'other')").fetchone()[0]
    conn.close()
    return jsonify(total_len=total)

@app.route("/audio/<path:filename>", methods=["GET"])
def serve_audio(filename):
    """提供音频文件。此部分不变。"""
    print(filename)
    full_path = "/" + filename
    return send_file(full_path)


@app.route("/export_to_jsonl", methods=["POST"])
def export_to_jsonl():
    """导出功能，将所有完成的数据导出回JSONL文件。"""
    print("正在将已完成任务导出到JSONL文件...")
    conn = get_db_connection()
    
    # 清空原有文件
    for path in [OTHER_JSON_PATH, SENSEVOICE_MARK_JSON_PATH, SEMANTIC_EMO_MARK_JSON_PATH]:
        with open(path, 'w', encoding='utf-8') as f:
            pass
    
    # 导出其他任务
    with open(OTHER_JSON_PATH, 'w', encoding='utf-8') as f:
        for task in conn.execute("SELECT * FROM tasks WHERE status = 'other'").fetchall():
            task_dict = dict(task)
            
            # 如果target为空，跳过导出
            if not task_dict.get('target', '').strip():
                print(f"ID为{task_dict.get('id')}的任务文本为空，跳过导出")
                continue
                
            source_path = task_dict.get('source', '')
            key = f"mark_{os.path.basename(source_path)}" if source_path else ''
            
            export_data = {
                "emo_target": task_dict.get('emo_target', ''),
                "event_target": task_dict.get('event_target', ''),
                "key": key,
                "source": task_dict.get('source', ''),
                "source_len": task_dict.get('source_len', 0),
                "target": task_dict.get('target', ''),
                "target_len": task_dict.get('target_len', 0),
                "text_language": task_dict.get('text_language', ''),
                "with_or_wo_itn": task_dict.get('with_or_wo_itn')
            }
            if task_dict.get('semantic_emo_target'):
                export_data["semantic_emo_target"] = task_dict.get('semantic_emo_target')
                
            f.write(json.dumps(export_data, ensure_ascii=False) + '\n')
    
    # 导出completed任务
    with open(SENSEVOICE_MARK_JSON_PATH, 'w', encoding='utf-8') as f_sv, \
         open(SEMANTIC_EMO_MARK_JSON_PATH, 'w', encoding='utf-8') as f_sem:
        for task in conn.execute("SELECT * FROM tasks WHERE status = 'completed'").fetchall():
            task_dict = dict(task)
            
            # 如果target为空，跳过导出
            if not task_dict.get('target', '').strip():
                print(f"ID为{task_dict.get('id')}的任务文本为空，跳过导出")
                continue
                
            semantic_emo_target = task_dict.get('semantic_emo_target')
            
            source_path = task_dict.get('source', '')
            key = f"mark_{os.path.basename(source_path)}" if source_path else ''
            
            # 按照新格式构造数据，不包含 semantic_emo_target
            export_data = {
                "emo_target": task_dict.get('emo_target', ''),
                "event_target": task_dict.get('event_target', ''),
                "key": key,
                "source": task_dict.get('source', ''),
                "source_len": task_dict.get('source_len', 0),
                "target": task_dict.get('target', ''),
                "target_len": task_dict.get('target_len', 0),
                "text_language": task_dict.get('text_language', ''),
                "with_or_wo_itn": task_dict.get('with_or_wo_itn')
            }
                
            f_sv.write(json.dumps(export_data, ensure_ascii=False) + '\n')
            
            # 如果存在，单独写入 semantic 文件
            if semantic_emo_target:
                f_sem.write(json.dumps({
                    "sentence": task_dict.get('target'),
                    "semantic_emo_target": semantic_emo_target,
                }, ensure_ascii=False) + '\n')
                
    conn.close()
    return jsonify(message=f"数据导出成功。")


# 启动时执行一次完整导出
def initial_export_all():
    """程序启动时将所有已完成任务放入导出队列。"""
    if REAL_TIME_EXPORT:
        print("程序启动时，准备初始数据进行异步导出...")
        
        # 清空现有文件
        for path in [OTHER_JSON_PATH, SENSEVOICE_MARK_JSON_PATH, SEMANTIC_EMO_MARK_JSON_PATH]:
            if os.path.exists(path):
                open(path, 'w', encoding='utf-8').close()
                print(f"已清空文件: {path}")
        
        # 将所有已完成和other状态的任务放入队列
        conn = get_db_connection()
        try:
            tasks_to_export = conn.execute("SELECT * FROM tasks WHERE status IN ('completed', 'other')").fetchall()
            print(f"发现 {len(tasks_to_export)} 个历史任务需要导出。")
            for task in tasks_to_export:
                export_queue.put(dict(task))
            print("所有历史任务已加入异步导出队列。")
        except Exception as e:
            print(f"初始数据导出准备失败: {e}")
        finally:
            conn.close()


if __name__ == "__main__":
    init_db()
    
    # 启动异步导出工作线程
    export_thread = threading.Thread(target=export_worker, daemon=True)
    export_thread.start()
    
    # 程序启动时进行一次完整导出（放入队列）
    initial_export_all()
    
    cleanup_thread = threading.Thread(target=cleanup_timed_out_tasks, daemon=True)
    cleanup_thread.start()
    
    # 现在可以安全地以多线程模式运行，以获得更好的并发性能
    app.run(host="0.0.0.0", port=5002, threaded=True) 