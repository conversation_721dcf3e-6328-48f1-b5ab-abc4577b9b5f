import mysql.connector
from mysql.connector import Error
from datetime import datetime

def save_chat_data_to_mysql(data_dict, host='localhost', user='root', password='20240803', database='xiaoanchattest', table='chatdata_log'):
    """
    将聊天数据存入MySQL数据库的指定表的同一行
    
    参数:
        data_dict (dict): 包含要存储的字段数据的字典
        host (str): MySQL服务器地址
        user (str): MySQL用户名
        password (str): MySQL密码
        database (str): 数据库名称
        table (str): 表名
    
    返回:
        tuple: (bool, int) 操作成功返回(True, record_id)，失败返回(False, None)
    """
    try:
        # 建立数据库连接
        connection = mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            database=database
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # 如果没有提供chat_time，使用当前时间
            if 'chat_time' not in data_dict:
                data_dict['chat_time'] = datetime.now()
            
            # 构建SQL插入语句
            fields = list(data_dict.keys())
            placeholders = ', '.join(['%s'] * len(fields))
            field_names = ', '.join(fields)
            
            sql = f"INSERT INTO {table} ({field_names}) VALUES ({placeholders})"
            values = tuple(data_dict.values())
            
            # 执行SQL语句
            cursor.execute(sql, values)
            
            # 获取插入的记录ID
            record_id = cursor.lastrowid
            
            # 提交更改
            connection.commit()
            
            print(f"聊天数据已成功存入 {database}.{table}，记录ID: {record_id}")
            return True, record_id
            
    except Error as e:
        print(f"MySQL连接错误: {e}")
        return False, None
    finally:
        # 关闭连接
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()
            print("MySQL连接已关闭")

def update_chat_data_in_mysql(record_id, data_dict, host='localhost', user='root', password='20240803', database='xiaoanchattest', table='chatdata_log'):
    """
    更新MySQL数据库中指定记录的数据
    
    参数:
        record_id (int): 要更新的记录ID
        data_dict (dict): 包含要更新的字段数据的字典
        host (str): MySQL服务器地址
        user (str): MySQL用户名
        password (str): MySQL密码
        database (str): 数据库名称
        table (str): 表名
    
    返回:
        bool: 操作成功返回True，失败返回False
    """
    try:
        # 建立数据库连接
        connection = mysql.connector.connect(
            host=host,
            user=user,
            password=password,
            database=database
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # 构建SQL更新语句
            set_clauses = []
            values = []
            for field, value in data_dict.items():
                set_clauses.append(f"{field} = %s")
                values.append(value)
            
            set_clause = ', '.join(set_clauses)
            sql = f"UPDATE {table} SET {set_clause} WHERE id = %s"
            values.append(record_id)
            
            # 执行SQL语句
            cursor.execute(sql, tuple(values))
            
            # 提交更改
            connection.commit()
            
            print(f"记录ID {record_id} 已成功更新")
            return True
            
    except Error as e:
        print(f"MySQL连接错误: {e}")
        return False
    finally:
        # 关闭连接
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()
            print("MySQL连接已关闭")

# 保持向后兼容的旧函数
def save_text_to_mysql(text, host='localhost', user='root', password='20240803', database='xiaoanchattest', table='chatdata_log', field='text_content'):
    """
    将文本存入MySQL数据库的指定表的指定字段（向后兼容函数）
    """
    data_dict = {field: text}
    success, record_id = save_chat_data_to_mysql(data_dict, host, user, password, database, table)
    return success

# 使用示例
if __name__ == "__main__":
    text_to_save = "nihao"
    save_text_to_mysql(
        text=text_to_save,
        field='user_input'       # 替换为你的字段名
    )
