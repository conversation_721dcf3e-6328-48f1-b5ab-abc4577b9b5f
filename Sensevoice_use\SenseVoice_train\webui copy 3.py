import os  # 导入操作系统模块，用于文件和目录操作
import threading  # 导入线程模块，用于多线程处理
from fastapi import FastAPI  # 导入FastAPI框架，用于创建Web应用
from fastapi.responses import HTMLResponse  # 导入HTML响应类型
import librosa  # 导入音频处理库
import base64  # 导入base64编码解码库
import io  # 导入输入输出流处理库
import gradio as gr  # 导入Gradio库，用于创建Web界面
import re  # 导入正则表达式库
import numpy as np  # 导入NumPy库，用于数值计算
import torch  # 导入PyTorch库，用于深度学习
import torchaudio  # 导入PyTorch音频处理库
from funasr import AutoModel  # 导入自动模型加载功能

# 在此输入两个对比的SenseVoice模型路径
model_dir1 = "iic/SenseVoiceSmall"  # 第一个模型路径（官方模型）
model_dir2 = "/home/<USER>/emotion2vec/SenseVoice/outputs_241122"  # 第二个模型路径（自定义训练的模型）

# 加载第一个模型
model1 = AutoModel(
    model=model_dir1,  # 模型路径
    trust_remote_code=True,  # 信任远程代码
    remote_code="./model.py",  # 远程代码路径
    device="cuda:0",  # 使用GPU加速
    disable_update=True,  # 禁用更新
    vad_model="iic/speech_fsmn_vad_zh-cn-16k-common-pytorch",  # 语音活动检测模型
    vad_kwargs={"max_single_segment_time": 30000},  # VAD参数设置
    ban_emo_unk=True,  # 禁止未知情感标签
    output_token_probs=True  # 输出标记概率
)

# 加载第二个模型
model2 = AutoModel(
    model=model_dir2,  # 使用自定义训练的模型路径
    trust_remote_code=True,  # 信任远程代码
    remote_code="./model.py",  # 远程代码路径
    device="cuda:0",  # 使用GPU加速
    disable_update=True,  # 禁用更新
    vad_model="iic/speech_fsmn_vad_zh-cn-16k-common-pytorch",  # 语音活动检测模型
    vad_kwargs={"max_single_segment_time": 30000},  # VAD参数设置
    ban_emo_unk=True,  # 禁止未知情感标签
    output_token_probs=True  # 输出标记概率
)

# 定义情感标签和对应的表情符号字典
emo_dict = {
    "<|HAPPY|>": "😊",  # 高兴
    "<|SAD|>": "😔",  # 悲伤
    "<|ANGRY|>": "😡",  # 愤怒
    "<|NEUTRAL|>": "",  # 中性（无表情）
    "<|FEARFUL|>": "😰",  # 恐惧
    "<|DISGUSTED|>": "🤢",  # 厌恶
    "<|SURPRISED|>": "😮",  # 惊讶
}

# 定义事件标签和对应的表情符号字典
event_dict = {
    "<|BGM|>": "🎼",  # 背景音乐
    "<|Speech|>": "",  # 语音（无特殊标记）
    "<|Applause|>": "👏",  # 掌声
    "<|Laughter|>": "😀",  # 笑声
    "<|Cry|>": "😭",  # 哭声
    "<|Sneeze|>": "🤧",  # 打喷嚏
    "<|Breath|>": "",  # 呼吸声（无特殊标记）
    "<|Cough|>": "🤧",  # 咳嗽
}

# 定义所有标签和对应表情符号的完整字典
emoji_dict = {
    "<|nospeech|><|Event_UNK|>": "❓",  # 无语音且事件未知
    "<|zh|>": "",  # 中文（无特殊标记）
    "<|en|>": "",  # 英文（无特殊标记）
    "<|yue|>": "",  # 粤语（无特殊标记）
    "<|ja|>": "",  # 日语（无特殊标记）
    "<|ko|>": "",  # 韩语（无特殊标记）
    "<|nospeech|>": "",  # 无语音（无特殊标记）
    "<|HAPPY|>": "😊",  # 高兴
    "<|SAD|>": "😔",  # 悲伤
    "<|ANGRY|>": "😡",  # 愤怒
    "<|NEUTRAL|>": "",  # 中性（无表情）
    "<|BGM|>": "🎼",  # 背景音乐
    "<|Speech|>": "",  # 语音（无特殊标记）
    "<|Applause|>": "👏",  # 掌声
    "<|Laughter|>": "😀",  # 笑声
    "<|FEARFUL|>": "😰",  # 恐惧
    "<|DISGUSTED|>": "🤢",  # 厌恶
    "<|SURPRISED|>": "😮",  # 惊讶
    "<|Cry|>": "😭",  # 哭声
    "<|EMO_UNKNOWN|>": "",  # 未知情感（无特殊标记）
    "<|Sneeze|>": "🤧",  # 打喷嚏
    "<|Breath|>": "",  # 呼吸声（无特殊标记）
    "<|Cough|>": "😷",  # 咳嗽
    "<|Sing|>": "",  # 唱歌（无特殊标记）
    "<|Speech_Noise|>": "",  # 语音噪音（无特殊标记）
    "<|withitn|>": "",  # 使用ITN（无特殊标记）
    "<|woitn|>": "",  # 不使用ITN（无特殊标记）
    "<|GBG|>": "",  # 垃圾语音（无特殊标记）
    "<|Event_UNK|>": "",  # 未知事件（无特殊标记）
}

# 定义语言标签字典
lang_dict = {
    "<|zh|>": "<|lang|>",  # 中文
    "<|en|>": "<|lang|>",  # 英文
    "<|yue|>": "<|lang|>",  # 粤语
    "<|ja|>": "<|lang|>",  # 日语
    "<|ko|>": "<|lang|>",  # 韩语
    "<|nospeech|>": "<|lang|>",  # 无语音
}

# 定义情感和事件表情符号集合，用于后续处理
emo_set = {"😊", "😔", "😡", "😰", "🤢", "😮"}  # 情感表情符号集合
event_set = {"🎼", "👏", "😀", "😭", "🤧",}  # 事件表情符号集合

# 格式化字符串函数（简单替换标签为表情符号）
def format_str(s):
    """
    将标签替换为对应的表情符号
    
    参数:
    s - 包含标签的字符串
    
    返回:
    替换后的字符串
    """
    for sptk in emoji_dict:
        s = s.replace(sptk, emoji_dict[sptk])
    return s

# 格式化字符串函数（版本2，更复杂的处理）
def format_str_v2(s):
    """
    更复杂的格式化处理，统计标签出现次数并选择主要情感
    
    参数:
    s - 包含标签的字符串
    
    返回:
    处理后的字符串，带有事件和情感表情符号
    """
    sptk_dict = {}  # 创建一个字典存储各标签出现次数
    for sptk in emoji_dict:
        sptk_dict[sptk] = s.count(sptk)  # 统计每个标签出现的次数
        s = s.replace(sptk, "")  # 从字符串中移除标签
    
    emo = "<|NEUTRAL|>"  # 默认情感为中性
    for e in emo_dict:
        if sptk_dict[e] > sptk_dict[emo]:  # 如果某种情感出现次数更多
            emo = e  # 更新主要情感
    
    for e in event_dict:
        if sptk_dict[e] > 0:  # 如果存在某种事件
            s = event_dict[e] + s  # 在文本前添加事件表情符号
    
    s = s + emo_dict[emo]  # 在文本后添加情感表情符号

    # 调整表情符号和空格的位置
    for emoji in emo_set.union(event_set):
        s = s.replace(" " + emoji, emoji)  # 移除表情符号前的空格
        s = s.replace(emoji + " ", emoji)  # 移除表情符号后的空格
    
    return s.strip()  # 去除首尾空格并返回

# 格式化字符串函数（版本3，处理多语言文本）
def format_str_v3(s):
    """
    处理多语言文本，保持情感和事件表情符号的一致性
    
    参数:
    s - 包含标签的字符串
    
    返回:
    处理后的字符串，带有适当的表情符号
    """
    # 定义获取情感和事件表情符号的辅助函数
    def get_emo(s):
        return s[-1] if s[-1] in emo_set else None  # 获取字符串最后一个字符，如果是情感表情符号则返回
    
    def get_event(s):
        return s[0] if s[0] in event_set else None  # 获取字符串第一个字符，如果是事件表情符号则返回

    s = s.replace("<|nospeech|><|Event_UNK|>", "❓")  # 替换无语音且事件未知的标签为问号
    
    for lang in lang_dict:
        s = s.replace(lang, "<|lang|>")  # 将所有语言标签统一替换为<|lang|>
    
    s_list = [format_str_v2(s_i).strip(" ") for s_i in s.split("<|lang|>")]  # 按语言标签分割并处理每段文本
    
    new_s = " " + s_list[0]  # 初始化结果字符串
    cur_ent_event = get_event(new_s)  # 获取当前事件表情符号
    
    # 处理后续的文本段落
    for i in range(1, len(s_list)):
        if len(s_list[i]) == 0:  # 如果段落为空，跳过
            continue
        
        # 如果当前段落和前一段落有相同的事件表情符号，移除当前段落的事件表情符号
        if get_event(s_list[i]) == cur_ent_event and get_event(s_list[i]) != None:
            s_list[i] = s_list[i][1:]
        
        cur_ent_event = get_event(s_list[i])  # 更新当前事件表情符号
        
        # 如果当前段落和结果字符串有相同的情感表情符号，移除结果字符串的情感表情符号
        if get_emo(s_list[i]) != None and get_emo(s_list[i]) == get_emo(new_s):
            new_s = new_s[:-1]
        
        new_s += s_list[i].strip().lstrip()  # 添加当前段落到结果字符串
    
    new_s = new_s.replace("The.", " ")  # 移除"The."
    return new_s.strip()  # 去除首尾空格并返回

# 推理函数（使用两个模型进行对比）
def model_inference(input_wav, language, fs=16000):
    """
    使用两个模型对音频进行推理，识别语音内容和情感
    
    参数:
    input_wav - 输入音频数据
    language - 语言选择
    fs - 采样率，默认16000Hz
    
    返回:
    两个模型的文本结果和情感分析结果
    """
    # 语言缩写字典
    language_abbr = {
        "auto": "auto", "zh": "zh", "en": "en", "yue": "yue", "ja": "ja", "ko": "ko", "nospeech": "nospeech"
    }
    language = "auto" if len(language) < 1 else language  # 如果未指定语言，使用自动检测
    selected_language = language_abbr[language]  # 获取语言缩写
    
    # 处理输入音频数据
    if isinstance(input_wav, tuple):
        fs, input_wav = input_wav  # 如果输入是元组，解包为采样率和音频数据
        input_wav = input_wav.astype(np.float32) / np.iinfo(np.int16).max  # 将音频数据归一化到[-1,1]
        if len(input_wav.shape) > 1:
            input_wav = input_wav.mean(-1)  # 如果是多声道，转换为单声道
        if fs != 16000:
            # 如果采样率不是16000Hz，进行重采样
            resampler = torchaudio.transforms.Resample(fs, 16000)
            input_wav_t = torch.from_numpy(input_wav).to(torch.float32)
            input_wav = resampler(input_wav_t[None, :])[0, :].numpy()

    # 使用模型1进行推理
    res1 = model1.generate(input=input_wav,
                           cache={},
                           language=language,
                           use_itn=True,  # 使用ITN（逆文本规范化）
                           ban_emo_unk=True,  # 禁止未知情感标签
                           batch_size_s=60,  # 批处理大小
                           merge_vad=True)  # 合并VAD结果
    
    # 处理模型1的结果
    text1 = format_str_v3(res1[0]["text"])  # 格式化文本结果
    emotion1 = res1[0]["emotion"]  # 获取情感分析结果
    total1 = sum(emotion1.values())  # 计算情感概率总和
    for emotion_key in emotion1:
        emotion1[emotion_key] /= total1  # 将情感概率归一化
    
    # 使用模型2进行推理
    res2 = model2.generate(input=input_wav,
                           cache={},
                           language=language,
                           use_itn=True,  # 使用ITN（逆文本规范化）
                           ban_emo_unk=True,  # 禁止未知情感标签
                           batch_size_s=60,  # 批处理大小
                           merge_vad=True)  # 合并VAD结果

    # 处理模型2的结果
    text2 = format_str_v3(res2[0]["text"])  # 格式化文本结果
    emotion2 = res2[0]["emotion"]  # 获取情感分析结果
    total2 = sum(emotion2.values())  # 计算情感概率总和
    for emotion_key in emotion2:
        emotion2[emotion_key] /= total2  # 将情感概率归一化

    return text1, emotion1, text2, emotion2  # 返回两个模型的结果

# 音频示例列表，用于界面展示
audio_examples = [
    ["/home/<USER>/emotion2vec/data/mp3/session4/segment_754.mp3", "zh","SAD"],  # 悲伤示例
    ["/home/<USER>/emotion2vec/data/mp3/session4/segment_1079.mp3","zh","ANGRY"],  # 愤怒示例
    ["/home/<USER>/emotion2vec/data/mp3/session4/segment_490.mp3","zh","ANGRY"],  # 愤怒示例
    ["/home/<USER>/emotion2vec/data/mp3/session4/segment_347.mp3","zh","DISGUSTED"],  # 厌恶示例
    ["/home/<USER>/emotion2vec/data/mp3/session4/segment_410.mp3","zh","SURPRISED"],  # 惊讶示例
    ["/home/<USER>/emotion2vec/data/mp3/session4/segment_42.mp3","zh","HAPPT"],  # 高兴示例
    ["/home/<USER>/emotion2vec/data/mp3/session4/segment_1093.mp3","zh","SAD"],  # 悲伤示例
    ["/home/<USER>/emotion2vec/data/mp3/session4/segment_54.mp3","zh","SAD"],  # 悲伤示例
    ["/home/<USER>/emotion2vec/data/mp3/session4/segment_143.mp3","zh","ANGRY"],  # 愤怒示例
    ["/home/<USER>/emotion2vec/data/mp3/session4/segment_1296.mp3", "zh","ANGRY"],  # 愤怒示例
    ["/home/<USER>/emotion2vec/data/mp3/session4/segment_475.mp3", "zh","HAPPY"],  # 高兴示例
    ["/home/<USER>/emotion2vec/data/mp3/session4/segment_933.mp3", "zh","HAPPY"],  # 高兴示例
    ["/home/<USER>/emotion2vec/data/mp3/session4/segment_140.mp3", "zh","HAPPY"],  # 高兴示例
    ["/home/<USER>/emotion2vec/data/mp3/session1/segment_5140.mp3", "zh","HAPPY"],  # 高兴示例
    ["/home/<USER>/emotion2vec/data/mp3/session1/segment_3931.mp3", "zh","HAPPY"],  # 高兴示例
    ["/home/<USER>/emotion2vec/data/mp3/session1/segment_951.mp3", "zh","HAPPY"],  # 高兴示例
    ["/home/<USER>/emotion2vec/data/mp3/session1/segment_467.mp3", "zh","HAPPY"],  # 高兴示例
    ["/home/<USER>/emotion2vec/data/mp3/session4/segment_397.mp3", "zh","HAPPY"],  # 高兴示例
    ["/home/<USER>/emotion2vec/data/mp3/session1/segment_2240.mp3", "zh", "ANGRY"],  # 愤怒示例
    ["/home/<USER>/emotion2vec/data/mp3/session1/segment_2034.mp3", "zh", "SAD"],  # 悲伤示例
    ["/home/<USER>/emotion2vec/data/mp3/session1/segment_722.mp3", "zh", "HAPPY"],  # 高兴示例
    ["/home/<USER>/emotion2vec/data/mp3/session1/segment_4120.mp3", "zh", "HAPPY"],  # 高兴示例
    ["/home/<USER>/emotion2vec/data/mp3/session4/segment_751.mp3", "zh", "SAD"],  # 悲伤示例
    ["/home/<USER>/emotion2vec/data/mp3/session1/segment_2857.mp3", "zh", "HAPPY"],  # 高兴示例
    ["/home/<USER>/emotion2vec/data/mp3/session1/segment_1961.mp3", "zh", "HAPPY"],  # 高兴示例
    ["/home/<USER>/emotion2vec/data/mp3/session1/segment_5112.mp3", "zh", "HAPPY"],  # 高兴示例
    ["/home/<USER>/emotion2vec/data/mp3/session1/segment_4899.mp3", "zh", "HAPPY"],  # 高兴示例
    ["/home/<USER>/emotion2vec/data/mp3/session1/segment_1234.mp3", "zh", "HAPPY"],  # 高兴示例
    ["/home/<USER>/emotion2vec/data/mp3/session1/segment_180.mp3", "zh", "ANGRY"],  # 愤怒示例
    ["/home/<USER>/emotion2vec/data/mp3/session4/segment_67.mp3", "zh", "ANGRY"],  # 愤怒示例
    ["/home/<USER>/emotion2vec/data/mp3/session4/segment_1188.mp3", "zh", "SAD"],  # 悲伤示例
    ["/home/<USER>/emotion2vec/data/mp3/session1/segment_1101.mp3", "zh", "HAPPY"],  # 高兴示例
    ["/home/<USER>/emotion2vec/data/mp3/session4/segment_145.mp3", "zh", "ANGRY"],  # 愤怒示例
    ["/home/<USER>/emotion2vec/data/mp3/session1/segment_2169.mp3", "zh", "SURPRISED"]  # 惊讶示例

    # 以下是原始代码中注释掉的示例
    # ["example/emo_1.wav", "auto"],
    # ["example/emo_2.wav", "auto"],
    # ["example/emo_3.wav", "auto"],
    # ["example/emo_4.wav", "auto"],
    # ["example/event_1.wav", "auto"],
    # ["example/event_2.wav", "auto"],
    # ["example/event_3.wav", "auto"],
    # ["example/rich_1.wav", "auto"],
    # ["example/rich_2.wav", "auto"],
    # ["example/rich_3.wav", "auto"],
    # ["example/longwav_1.wav", "auto"],
    # ["example/longwav_2.wav", "auto"],
    # ["example/longwav_3.wav", "auto"],
    #["example/longwav_4.wav", "auto"],
]

# 网页界面的HTML内容
html_content = """
<div>
    <h2 style="font-size: 22px;margin-left: 0px;">语音情感识别测试</h2>
</div>
"""

# 启动Gradio界面
def launch():
    """
    创建并启动Gradio Web界面
    """
    share = True  # 设置是否共享界面
    languages = [example[1] for example in audio_examples]  # 获取示例中的语言列表
    
    # 创建Gradio界面
    with gr.Blocks(theme=gr.themes.Soft()) as demo:
        gr.HTML(html_content)  # 添加HTML标题
        
        with gr.Row():  # 创建一行
            with gr.Column():  # 创建一列
                audio_inputs = gr.Audio(label="Upload audio or use the microphone")  # 添加音频输入组件
                
                with gr.Accordion("Configuration"):  # 创建可折叠的配置面板
                    language_inputs = gr.Dropdown(choices=languages, value="auto", label="Language")  # 添加语言选择下拉框
                    
                fn_button = gr.Button("Start", variant="primary")  # 添加开始按钮
                with gr.Row():  # 创建一行（用于显示结果）
                    with gr.Column():  # 第一列（模型1结果）
                        text_outputs1 = gr.Textbox(label="Model 1 Text Results")  # 文本结果显示框
                        emotion_score1 = gr.Label(label="Model 1 Emotion Scores")  # 情感分数显示框
                    with gr.Column():  # 第二列（模型2结果）
                        text_outputs2 = gr.Textbox(label="Model 2 Text Results")  # 文本结果显示框
                        emotion_score2 = gr.Label(label="Model 2 Emotion Scores")  # 情感分数显示框
                
                # 添加示例区域
                gr.Examples(examples=audio_examples, inputs=[audio_inputs, language_inputs], examples_per_page=20)
        
        # 设置按钮点击事件
        fn_button.click(model_inference, inputs=[audio_inputs, language_inputs],
                        outputs=[text_outputs1, emotion_score1, text_outputs2, emotion_score2])

    # 启动Gradio应用
    demo.launch(share=True, server_name='0.0.0.0', ssl_certfile="/home/<USER>/emotion2vec/cert.pem", 
                ssl_keyfile="/home/<USER>/emotion2vec/key.pem", ssl_verify=False, server_port=8094)

# 程序入口点
if __name__ == "__main__":
	# iface.launch()
	launch()  # 启动界面

