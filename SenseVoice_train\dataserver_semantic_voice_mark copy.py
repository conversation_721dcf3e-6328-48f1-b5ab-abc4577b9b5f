import time  # 导入时间模块，用于处理时间相关的操作
import uuid  # 导入UUID模块，用于生成全球唯一的标识符
from flask import (  # 导入Flask框架相关组件
    Flask,  # Flask核心类，用于创建Web应用
    json,  # 用于处理JSON数据
    jsonify,  # 将Python对象转换为JSON响应
    make_response,  # 创建自定义HTTP响应
    request,  # 处理HTTP请求
    send_file,  # 发送文件给用户
    send_from_directory,  # 从指定目录发送文件
    render_template,  # 渲染HTML模板
)
import os  # 导入操作系统模块，用于文件和目录操作

app = Flask(__name__)  # 创建Flask应用实例


# 定义各种JSON文件的路径
JSON_PATH = "/home/<USER>/emotion2vec/data/json/session1213/session.jsonl"  # 原始数据文件路径
SENSEVOICE_MARK_JSON_PATH = (  # SenseVoice标记后的数据保存路径
    "/home/<USER>/emotion2vec/data/json/session1213/sensevoice_marked.jsonl"
)
OTHER_JSON_PATH = "/home/<USER>/emotion2vec/data/json/session1213/other_marked.jsonl"  # 其他类型标记数据保存路径
SEMANTIC_EMO_MARK_JSON_PATH = (  # 语义情感标记数据保存路径
    "/home/<USER>/emotion2vec/data/json/session1213/sentantic_marked.jsonl"
)
modified_files = set()  # 创建一个集合，用于存储已修改的文件
import os  # 再次导入os模块（这行代码实际上是多余的）

print("Current working directory:", os.getcwd())  # 打印当前工作目录
current_file = None  # 当前处理的文件
total_len = 0  # 总文件数
current_line_index = 0  # 当前行索引


@app.route("/vvue")  # 定义Web路由"/vvue"
def index():
    """
    处理网站首页的请求
    为每个用户分配唯一ID并返回页面
    """
    # 检查用户是否已有分配的 user_id
    user_id = request.cookies.get("user_id")
    if not user_id:
        # 如果没有，生成一个新的 UUID
        user_id = str(uuid.uuid4())
    response = make_response(render_template("index_emo_copy.vue"))  # 渲染页面模板
    # 将 user_id 写入 Cookie，设置有效期为 30 天
    response.set_cookie("user_id", user_id, max_age=60 * 60 * 24 * 30)
    return response  # 返回响应


from datetime import datetime, timedelta  # 导入日期时间相关模块

import threading  # 导入线程模块，用于多线程处理

# 用于记录每条数据的分配状态
data_status = {}  # {key: {'user': None, 'timestamp': None}}

lock = threading.Lock()  # 确保状态更新线程安全（防止多个用户同时修改同一数据）
TIMEOUT = 1200  # 数据超时时间（秒），20分钟


@app.route("/get_file", methods=["GET"])  # 定义获取文件的API路由
def get_file():
    """
    获取待标记的文件
    为用户分配一个未处理的数据条目
    """
    global current_file, total_len  # 使用全局变量
    user_id = request.args.get("user_id")  # 从请求中获取用户 ID
    print(user_id)  # 打印用户ID（调试用）
    if current_file is None:
        current_file = JSON_PATH  # 如果当前文件未设置，使用默认文件路径

    with open(current_file, "r", encoding="utf-8") as f:  # 打开文件
        lines = f.readlines()  # 读取所有行
    total_len = len(lines)  # 计算总行数
    for line in lines:  # 遍历每一行
        json_data = json.loads(line)  # 将JSON字符串转换为Python对象
        key = json_data["source"]  # 假设每条数据有唯一键（source字段）

        with lock:  # 使用线程锁保护以下代码块
            # 检查数据是否已分配，若未分配或超时则分配给当前用户
            if (
                key not in data_status  # 如果数据未被分配
                or data_status[key]["user"] is None  # 或者数据没有用户
                or (datetime.now() - data_status[key]["timestamp"]).seconds > TIMEOUT  # 或者数据已超时
            ):
                data_status[key] = {"user": user_id, "timestamp": datetime.now()}  # 将数据分配给当前用户
                return jsonify(json_data=json_data)  # 返回数据给用户
            elif user_id == data_status[key]["user"]:  # 如果数据已分配给当前用户
                return jsonify(json_data=json_data)  # 返回数据给用户
    return jsonify(message="没有更多文件可修改。")  # 如果没有可用数据，返回提示信息


@app.route("/modify_file", methods=["POST"])  # 定义修改文件的API路由
def modify_file():
    """
    处理用户提交的标记数据
    保存标记结果并从未处理列表中删除该条目
    """
    data = request.json  # 获取请求中的JSON数据
    user_id = data["user_id"]  # 获取用户ID
    modified_audio_info = data["json_content"]  # 获取修改后的音频信息
    json_object = json.loads(modified_audio_info)  # 将JSON字符串转换为Python对象
    key = json_object["source"]  # 获取数据的唯一键

    with lock:  # 使用线程锁保护以下代码块
        # 检查数据是否属于该用户，且未超时
        if (
            key not in data_status  # 如果数据不在状态记录中
            or data_status[key]["user"] != user_id  # 或者数据不属于当前用户
            or (datetime.now() - data_status[key]["timestamp"]).seconds > TIMEOUT  # 或者数据已超时
        ):
            return jsonify(
                message="提交失败，数据无效或已超时，请刷新获取新数据。", code=500
            )  # 返回错误信息

    # 保存标注逻辑
    semantic_emo_target = json_object.get("semantic_emo_target", None)  # 获取语义情感标签
    target_len = json_object["target_len"]  # 获取目标长度
    if target_len != 0:  # 如果目标长度不为0（有内容需要处理）
        if (
            json_object["emo_target"] == "<|OTHER|>"  # 如果情感标签是"OTHER"
            or semantic_emo_target == "<|OTHER|>"  # 或者语义情感标签是"OTHER"
        ):
            # 保存到其他类型标记文件
            with open(OTHER_JSON_PATH, "a", encoding="utf-8") as f:
                f.write(json.dumps(json_object, ensure_ascii=False) + "\n")
        else:
            # 保存到SenseVoice标记文件
            with open(SENSEVOICE_MARK_JSON_PATH, "a", encoding="utf-8") as f:
                if semantic_emo_target is not None:  # 如果有语义情感标签
                    json_object.pop("semantic_emo_target")  # 移除语义情感标签（因为要单独保存）
                f.write(json.dumps(json_object, ensure_ascii=False) + "\n")

            if semantic_emo_target is not None:  # 如果有语义情感标签
                # 保存到语义情感标记文件
                with open(SEMANTIC_EMO_MARK_JSON_PATH, "a", encoding="utf-8") as f:
                    semantic_emo_data = {
                        "sentence": json_object["target"],  # 句子内容
                        "semantic_emo_target": semantic_emo_target,  # 语义情感标签
                    }
                    f.write(json.dumps(semantic_emo_data, ensure_ascii=False) + "\n")

    # 从未标记 JSONL 文件中删除该条数据
    remaining_lines = []  # 创建一个列表存储剩余行
    with open(JSON_PATH, "r", encoding="utf-8") as f:  # 打开原始文件
        for line in f:  # 遍历每一行
            audio_info = json.loads(line.strip())  # 解析JSON数据
            if str(audio_info["source"]) != json_object["source"]:  # 如果不是当前处理的数据
                remaining_lines.append(line)  # 保留该行

    with open(JSON_PATH, "w", encoding="utf-8") as f:  # 重新打开文件（写入模式）
        f.writelines(remaining_lines)  # 写入剩余行
    with lock:  # 使用线程锁保护以下代码块
        data_status[key]["user"] = None  # 释放数据（标记为未分配）
    return jsonify(message="文件修改成功。", code=200)  # 返回成功信息


def cleanup_data_status():
    """
    定期清理过期的数据状态
    这是一个后台运行的函数，每10分钟执行一次
    """
    while True:  # 无限循环
        with lock:  # 使用线程锁保护以下代码块
            keys_to_remove = []  # 创建一个列表存储要移除的键
            for key, info in data_status.items():  # 遍历所有数据状态
                if (
                    info["user"] is None  # 如果数据未分配
                    or (datetime.now() - info["timestamp"]).seconds > TIMEOUT  # 或者数据已超时
                ):
                    keys_to_remove.append(key)  # 添加到要移除的列表
            for key in keys_to_remove:  # 遍历要移除的键
                del data_status[key]  # 从数据状态中删除
        time.sleep(600)  # 休眠10分钟（600秒）


# 启动后台线程
threading.Thread(target=cleanup_data_status, daemon=True).start()  # 创建并启动清理线程（守护线程）


@app.route("/get_total", methods=["GET"])  # 定义获取总数的Aq'qPI路由
def get_total():
    """
    获取待处理数据的总数
    """
    global total_len  # 使用全局变量
    return jsonify(total_len=total_len)  # 返回总数


@app.route("/get_completedFiles", methods=["GET"])  # 定义获取已完成文件数的API路由
def get_completedFiles():
    """
    获取已完成标记的文件数量
    """
    with open(SENSEVOICE_MARK_JSON_PATH, "r", encoding="utf-8") as f:  # 打开已标记文件
        lines = f.readlines()  # 读取所有行
        total_len = len(lines)  # 计算总行数
    return jsonify(total_len=total_len)  # 返回总数


@app.route("/audio/<path:filename>", methods=["GET"])  # 定义获取音频文件的API路由
def serve_audio(filename):
    """
    提供音频文件下载服务
    """
    print(filename)  # 打印文件名（调试用）
    full_path = "/" + filename  # 构建完整路径
    return send_file(full_path)  # 发送文件给用户


if __name__ == "__main__":  # 如果直接运行此脚本
    app.run(host="0.0.0.0", port=5002)  # 启动Flask应用，监听所有网络接口的5002端口
