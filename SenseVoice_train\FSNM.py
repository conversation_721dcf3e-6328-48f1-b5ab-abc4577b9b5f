import os
import shutil 
from funasr import AutoModel
from pydub import AudioSegment
import subprocess

class SegmentProcess():
    """切分工具类
    用于音视频文件的格式转换、音频分割、临时文件清理等操作。
    """
    def __init__(self,vedio_dir,out_mp3_dir,temp_dir,mp4_to_mp3_flag):
        # 初始化，加载 VAD 模型，设置输入输出和临时目录
        self.model = AutoModel(model="fsmn-vad", model_revision="v2.0.4",disable_update=True)
        self.vedio_dir = vedio_dir  # 视频或音频输入目录/文件
        self.out_mp3_dir = out_mp3_dir  # 分割后音频输出目录
        self.temp_dir = temp_dir  # 临时文件目录
        self.mp4_to_mp3_flag = mp4_to_mp3_flag  # 是否需要 mp4 转 mp3 标志
    def mp4_to_mp3(self,vedio_dir,out_mp3_dir):
        # 内部函数：调用 ffmpeg 将音频/视频文件转为 mp3 格式
        def ffprocess_mp4_to_mp3(input_file, output_file):
            command = [
                'ffmpeg', '-i', input_file, 
                '-b:a', '192k',      # 设置比特率为 192k
                '-ac', '1',          # 设置声道数为 1（单声道）
                '-ar', '16000',      # 设置采样率为 16000 Hz
                '-threads', str(20), # 设置线程数
                '-c:a', 'mp3',       # 设置音频编码格式为 mp3
                output_file
            ]
            try:
                subprocess.run(command, check=True)  # 执行命令
                print(f"{output_file} 转换成功！")
            except subprocess.CalledProcessError as e:
                print(f"转换失败: {e}")
        # 判断输入是单个文件还是目录
        if os.path.isfile(vedio_dir):
            conv_path= "conv_"+os.path.basename(vedio_dir).replace(".mp4", ".mp3")
            # 如果是单个文件，直接转换为 .mp3
            mp3_path = os.path.join(out_mp3_dir,conv_path)
            ffprocess_mp4_to_mp3(vedio_dir,mp3_path)
            return mp3_path
        else:
            # 遍历输入目录
            for file in os.listdir(vedio_dir):
                file_ext = os.path.splitext(file)[1].lower()  # 获取文件扩展名并转为小写
                input_path = os.path.join(vedio_dir, file)
                if file_ext == ".mp4":
                    # 如果是 .mp4 文件，转换为 .mp3
                    mp3_path = os.path.join(out_mp3_dir, file.replace(".mp4", ".mp3"))
                    if not os.path.exists(mp3_path):
                        ffprocess_mp4_to_mp3(input_path, mp3_path)
                if file_ext == ".wav":
                    # 如果是 .wav 文件，转换为 .mp3
                    mp3_path = os.path.join(out_mp3_dir, file.replace(".wav", ".mp3"))
                    if not os.path.exists(mp3_path):
                        ffprocess_mp4_to_mp3(input_path, mp3_path)
                elif file_ext == ".mp3":
                    # 如果是 .mp3 文件，直接复制
                    mp3_path = os.path.join(out_mp3_dir, file)
                    if not os.path.exists(mp3_path):
                        shutil.copy2(input_path, mp3_path)
                        print(f"{file} 已复制到 {out_mp3_dir}")
                else:
                    print(f"跳过不支持的文件类型: {file}")
                    
    def segmentProcess(self,mp3_dir,out_mp3_dir):
        # 音频分割主流程，支持单文件和目录
        if os.path.isfile(mp3_dir):
            # 如果是单个文件，直接分割
            res = self.model.generate(input=mp3_dir,max_end_silence_time=1000)  # VAD 检测静音段
            audio = AudioSegment.from_file(mp3_dir)  # 加载音频文件
            i=0   # 分段计数器
            for  (start, end) in res[0]["value"]:
                    duration = end - start  # 计算持续时间
                    if duration >= 2000 and duration<30000:  # 判断是否大于 2 秒且小于 30 秒
                        segment = audio[start:end]  # 截取音频片段
                        segment = segment.set_channels(1).set_frame_rate(16000)  # 设置单声道和采样率
                        segment.export(f"{out_mp3_dir}/segment_{i + 1}.mp3", format="mp3")  # 保存为 mp3 文件
                        print(f"已导出：segment_{i + 1}.mp3（时长：{duration / 1000:.2f} 秒）")
                        i+=1
                    elif duration>=30000:
                        # 如果片段大于 30 秒，平分为两段
                        mid = duration/2
                        segment1 = audio[start:start+mid]
                        segment2 = audio[start+mid:end]
                        segment1 = segment1.set_channels(1).set_frame_rate(16000)
                        segment2 = segment2.set_channels(1).set_frame_rate(16000)
                        segment1.export(f"{out_mp3_dir}/segment_{i + 1}.mp3", format="mp3")
                        segment2.export(f"{out_mp3_dir}/segment_{i + 1}_2.mp3", format="mp3")
                        print(f"已导出：segment_{i + 1}.mp3（时长：{mid/ 1000:.2f} 秒）")
                        print(f"已导出：segment_2_{i + 1}.mp3（时长：{mid/ 1000:.2f} 秒）")
                        i+=1
            return out_mp3_dir
        i=0
        for file in os.listdir(mp3_dir):
            if file.endswith(".mp3"):
                mp3_path=os.path.join(mp3_dir,file)
                res = self.model.generate(input=mp3_path,max_end_silence_time=1000)  # VAD 检测静音段
                audio = AudioSegment.from_file(mp3_path)  # 加载音频文件
                for  (start, end) in res[0]["value"]:
                    duration = end - start  # 计算持续时间
                    if duration >= 2000 and duration<30000:  # 判断是否大于 2 秒且小于 30 秒
                        segment = audio[start:end]  # 截取音频片段
                        segment = segment.set_channels(1).set_frame_rate(16000)
                        segment.export(f"{out_mp3_dir}/segment_{i + 1}.mp3", format="mp3")
                        print(f"已导出：segment_{i + 1}.mp3（时长：{duration / 1000:.2f} 秒）")
                        i+=1
                    elif duration>=30000:
                        # 如果片段大于 30 秒，平分为两段
                        mid = duration/2
                        segment1 = audio[start:start+mid]
                        segment2 = audio[start+mid:end]
                        segment1 = segment1.set_channels(1).set_frame_rate(16000)
                        segment2 = segment2.set_channels(1).set_frame_rate(16000)
                        segment1.export(f"{out_mp3_dir}/segment_{i + 1}.mp3", format="mp3")
                        segment2.export(f"{out_mp3_dir}/segment_{i + 1}_2.mp3", format="mp3")
                        print(f"已导出：segment_{i + 1}.mp3（时长：{mid/ 1000:.2f} 秒）")
                        print(f"已导出：segment_2_{i + 1}.mp3（时长：{mid/ 1000:.2f} 秒）")
                        i+=1
        print("音频分割完成！")
    def temp_clean(self):
        # 清理临时目录下的所有文件
        for file in os.listdir(self.temp_dir):
                os.remove(os.path.join(self.temp_dir,file))
    def segprocess_mp4_to_mp3(self):
        # 一键式处理：mp4/wav/mp3 转 mp3，分割音频，清理临时文件
        try:
                mp3_dir = self.mp4_to_mp3(self.vedio_dir,self.temp_dir)  # 格式转换
                self.segmentProcess(mp3_dir,self.out_mp3_dir)  # 分割音频
                self.temp_clean()  # 清理临时文件
                return self.out_mp3_dir
        except Exception as e:
            print(e)
            return False
    def get_Timestamp(self,mp3):
        # 获取音频的分割时间戳（静音段）
        res = self.model.generate(input=mp3,max_end_silence_time=1000)
        return res