from flask import Flask, request, jsonify
import requests
import json
import os
from datetime import datetime
from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess

from 2mysql import save_chat_data_to_mysql, update_chat_data_in_mysql

# 初始化SenseVoice模型
model_sv = AutoModel(
    # model='iic/SenseVoiceSmall',
    model='/home/<USER>/emotion2vec/SenseVoice/outputs_250708_ft25',
    vad_model="fsmn-vad",
    vad_kwargs={"max_single_segment_time": 30000},
    device="cuda:0",
)

# Dify API配置
url_dify = 'http://localhost:11333/v1/chat-messages'
api_key = 'app-VAuPDvv6KKvUhz7MnpBBMdWW'

# 音频保存配置
AUDIO_SAVE_DIR = './audio_files'  # 默认保存目录，可通过环境变量或配置修改

app = Flask(__name__)

def ensure_audio_dir():
    """确保音频保存目录存在"""
    if not os.path.exists(AUDIO_SAVE_DIR):
        os.makedirs(AUDIO_SAVE_DIR)
        print(f"创建音频保存目录: {AUDIO_SAVE_DIR}")

def get_audio_filename(original_filename, user_id="user"):
    """生成音频文件名"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 精确到毫秒
    base_name = os.path.splitext(original_filename)[0]
    return f"{user_id}_{timestamp}_{base_name}"


def call_dify_api(text: str, conversation_id: str = "", user_id: str = "user") -> dict:
    """调用Dify API处理文本"""
    json_data = {
        "inputs": {"tips": "无"},
        "query": text,
        "response_mode": "blocking",
        "conversation_id": conversation_id,
        "user": user_id,
        "files": []
    }
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer " + api_key
    }
    
    try:
        response = requests.post(url_dify, headers=headers, json=json_data)
        if response.status_code == 200:
            return json.loads(response.text)
        else:
            return {"error": f"API调用失败，状态码: {response.status_code}"}
    except Exception as e:
        return {"error": f"API调用异常: {str(e)}"}

@app.route("/text", methods=['POST'])
def handle_text():
    """处理文本输入"""
    try:
        # 获取JSON数据
        json_data = request.get_json()
        if not json_data or 'text' not in json_data:
            return jsonify({"error": "请提供text字段"}), 400
        
        text = json_data['text']
        conversation_id = json_data.get('conversation_id', '')
        user_id = json_data.get('user_id', 'user')
        
        if not text.strip():
            return jsonify({"error": "文本内容不能为空"}), 400
        
        print(f"收到文本: {text}")

        # 准备初始数据
        chat_data = {
            'user_input': text
        }
        
        # 先插入初始数据，获取记录ID
        success, record_id = save_chat_data_to_mysql(chat_data)
        if not success:
            return jsonify({"error": "数据库保存失败"}), 500
        
        # 调用Dify API
        dify_response = call_dify_api(text, conversation_id, user_id)
        
        if 'error' not in dify_response:
            answer = dify_response.get('answer', '抱歉，我无法理解您的问题')
            new_conversation_id = dify_response.get('conversation_id', conversation_id)
            
            print(f"Dify回复: {answer}")

            # 更新记录，添加AI输出
            update_data = {
                'ai_output': answer
            }
            update_chat_data_in_mysql(record_id, update_data)
            
            return jsonify({
                "response": answer,
                "conversation_id": new_conversation_id,
                "status": "success"
            })
        else:
            return jsonify({
                "error": dify_response['error'],
                "status": "failed"
            }), 500
            
    except Exception as e:
        return jsonify({"error": f"处理异常: {str(e)}"}), 500

@app.route("/audio", methods=['POST'])
def handle_audio():
    """处理音频输入"""
    try:
        # 检查是否有文件上传
        if 'file' not in request.files:
            return jsonify({"error": "请上传音频文件"}), 400
        
        file = request.files['file']
        if not file.filename:
            return jsonify({"error": "文件名不能为空"}), 400
        
        # 检查文件格式是否为WAV
        if not file.filename.lower().endswith('.wav'):
            return jsonify({"error": "只支持WAV格式的音频文件"}), 400
        
        # 获取其他参数
        conversation_id = request.form.get('conversation_id', '')
        user_id = request.form.get('user_id', 'user')
        
        print(f"收到WAV音频文件: {file.filename}")
        
        # 确保音频保存目录存在
        ensure_audio_dir()
        
        # 生成文件名并直接保存WAV文件
        audio_filename = get_audio_filename(file.filename, user_id)
        saved_wav_file = os.path.join(AUDIO_SAVE_DIR, f"{audio_filename}.wav")
        
        # 直接保存WAV文件
        file.save(saved_wav_file)
        print(f"WAV文件已保存: {saved_wav_file}")

        # 直接使用保存的WAV文件进行语音识别
        res = model_sv.generate(
            input=saved_wav_file,
            cache={},
            language="zh",
            use_itn=True,
            batch_size_s=60,
            merge_vad=True,
            merge_length_s=15,
        )
        
        text = rich_transcription_postprocess(res[0]["text"])
        print(f"语音识别结果: {text}")

        # 检查识别的文本是否有效
        if not text or not text.strip():
            # 删除保存的音频文件
            if os.path.exists(saved_wav_file):
                os.remove(saved_wav_file)
            return jsonify({"error": "语音识别未获得有效文本"}), 400

        # 准备初始数据，包含音频路径和识别文本
        chat_data = {
            'path': saved_wav_file,
            'user_input': text
        }
        
        # 插入数据，获取记录ID
        success, record_id = save_chat_data_to_mysql(chat_data)
        if not success:
            return jsonify({"error": "数据库保存失败"}), 500

        # 更新记录，添加识别的文本
        update_data = {
            'user_input': text
        }
        update_chat_data_in_mysql(record_id, update_data)

        # 调用Dify API处理识别的文本
        dify_response = call_dify_api(text, conversation_id, user_id)
        
        if 'error' not in dify_response:
            answer = dify_response.get('answer', '抱歉，我无法理解您的问题')
            new_conversation_id = dify_response.get('conversation_id', conversation_id)
            
            print(f"Dify回复: {answer}")

            # 更新记录，添加AI输出
            update_data = {
                'ai_output': answer
            }
            update_chat_data_in_mysql(record_id, update_data)
            
            return jsonify({
                "recognized_text": text,
                "response": answer,
                "conversation_id": new_conversation_id,
                "status": "success"
            })
        else:
            return jsonify({
                "recognized_text": text,
                "error": dify_response['error'],
                "status": "failed"
            }), 500
            
    except Exception as e:
        return jsonify({"error": f"处理异常: {str(e)}"}), 500

@app.route("/", methods=['GET'])
def index():
    """API说明页面"""
    return jsonify({
        "message": "Dify API 简化版",
        "endpoints": {
            "/text": {
                "method": "POST",
                "description": "处理文本输入",
                "parameters": {
                    "text": "必需，要处理的文本",
                    "conversation_id": "可选，会话ID",
                    "user_id": "可选，用户ID"
                }
            },
            "/audio": {
                "method": "POST",
                "description": "处理音频输入",
                "parameters": {
                    "file": "必需，WAV格式的音频文件",
                    "conversation_id": "可选，会话ID",
                    "user_id": "可选，用户ID"
                }
            }
        }
    })


if __name__ == '__main__':
    # 初始化音频保存目录
    ensure_audio_dir()
    
    print("启动Dify API简化版服务...")
    print("文本接口: POST /text")
    print("音频接口: POST /audio")
    print(f"音频保存目录: {AUDIO_SAVE_DIR}")
    app.run(host='0.0.0.0', port=12229,)
